import { Module } from '@nestjs/common';
import { CustomersAuditService } from './customers-audit.service';
import { PrismaModule } from '../../prisma/prisma.module';
import { ConfigModule } from '../../config/config.module';

@Module({
  imports: [
    PrismaModule,
    ConfigModule,
  ],
  providers: [
    CustomersAuditService,
  ],
  exports: [
    CustomersAuditService,
  ],
})
export class CustomersAuditModule {}
