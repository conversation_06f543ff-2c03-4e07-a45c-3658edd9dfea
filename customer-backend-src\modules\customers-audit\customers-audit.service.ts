import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { AuthUser } from '../auth-shared';
import { AuditLog, AuditAction, Prisma } from '@prisma/client';

export interface AuditLogOptions {
  customerId: string;
  action: AuditAction;
  entity: string;
  entityId?: string;
  oldValues?: any;
  newValues?: any;
  description?: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
}

export interface AuditLogFilterOptions {
  action?: AuditAction;
  entity?: string;
  customerId?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

export interface AuditLogStatistics {
  totalLogs: number;
  actionBreakdown: Record<string, number>;
  recentActions: number;
  entityBreakdown: Record<string, number>;
}

@Injectable()
export class CustomersAuditService {
  private readonly logger = new Logger(CustomersAuditService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Create a single audit log entry
   */
  async logAction(options: AuditLogOptions): Promise<AuditLog> {
    this.logger.log(`Creating audit log for ${options.action} on ${options.entity}`);

    try {
      const auditLog = await this.prisma.auditLog.create({
        data: {
          customerId: options.customerId,
          action: options.action,
          entity: options.entity,
          entityId: options.entityId,
          oldValues: options.oldValues ? JSON.stringify(options.oldValues) : undefined,
          newValues: options.newValues ? JSON.stringify(options.newValues) : undefined,
          description: options.description,
          metadata: options.metadata ? JSON.stringify(options.metadata) : undefined,
          ipAddress: options.ipAddress,
          userAgent: options.userAgent,
          sessionId: options.sessionId,
          createdAt: new Date(),
        },
      });

      return auditLog;
    } catch (error) {
      this.logger.error(`Failed to create audit log: ${error}`);
      throw error;
    }
  }

  /**
   * Get audit logs with filtering options
   */
  async getAuditLogs(customerId?: string, options?: AuditLogFilterOptions): Promise<AuditLog[]> {
    const where: Prisma.AuditLogWhereInput = {};

    if (customerId) {
      where.customerId = customerId;
    }

    if (options?.action) {
      where.action = options.action;
    }

    if (options?.entity) {
      where.entity = options.entity;
    }

    if (options?.customerId) {
      where.customerId = options.customerId;
    }

    if (options?.startDate || options?.endDate) {
      where.createdAt = {};
      if (options.startDate) {
        where.createdAt.gte = options.startDate;
      }
      if (options.endDate) {
        where.createdAt.lte = options.endDate;
      }
    }

    return this.prisma.auditLog.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      take: options?.limit || 100,
      skip: options?.offset || 0,
      include: {
        customer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });
  }

  /**
   * Get audit logs by customer ID
   */
  async getAuditLogsByUser(customerId: string, options?: AuditLogFilterOptions): Promise<AuditLog[]> {
    return this.getAuditLogs(customerId, options);
  }

  /**
   * Get audit log statistics
   */
  async getAuditLogStatistics(options?: AuditLogFilterOptions): Promise<AuditLogStatistics> {
    const where: Prisma.AuditLogWhereInput = {};

    if (options?.customerId) {
      where.customerId = options.customerId;
    }

    if (options?.startDate || options?.endDate) {
      where.createdAt = {};
      if (options.startDate) {
        where.createdAt.gte = options.startDate;
      }
      if (options.endDate) {
        where.createdAt.lte = options.endDate;
      }
    }

    // Get total count
    const totalLogs = await this.prisma.auditLog.count({ where });

    // Get action breakdown
    const actionGroups = await this.prisma.auditLog.groupBy({
      by: ['action'],
      where,
      _count: { action: true },
    });

    const actionBreakdown: Record<string, number> = {};
    actionGroups.forEach(group => {
      actionBreakdown[group.action] = group._count.action;
    });

    // Get entity breakdown
    const entityGroups = await this.prisma.auditLog.groupBy({
      by: ['entity'],
      where,
      _count: { entity: true },
    });

    const entityBreakdown: Record<string, number> = {};
    entityGroups.forEach(group => {
      entityBreakdown[group.entity] = group._count.entity;
    });

    // Get recent actions (last 24 hours)
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    const recentActions = await this.prisma.auditLog.count({
      where: {
        ...where,
        createdAt: {
          gte: yesterday,
        },
      },
    });

    return {
      totalLogs,
      actionBreakdown,
      entityBreakdown,
      recentActions,
    };
  }

  /**
   * Helper method to log customer actions with user context
   */
  async logCustomerAction(
    customerId: string,
    action: AuditAction,
    entity: string,
    entityId?: string,
    oldValues?: any,
    newValues?: any,
    user?: AuthUser,
    description?: string,
    metadata?: Record<string, any>,
  ): Promise<AuditLog> {
    return this.logAction({
      customerId,
      action,
      entity,
      entityId,
      oldValues,
      newValues,
      description,
      metadata: {
        ...metadata,
        userId: user?.id,
        userEmail: user?.email,
      },
    });
  }
}
