import { Prisma } from "@prisma/client";

export interface CategoryOperations {
  create: <T extends Prisma.CategoryCreateArgs>(
    args: Prisma.SelectSubset<T, Prisma.CategoryCreateArgs>
  ) => Prisma.PrismaPromise<Prisma.CategoryGetPayload<T>>;

  findMany: <T extends Prisma.CategoryFindManyArgs>(
    args?: Prisma.SelectSubset<T, Prisma.CategoryFindManyArgs>
  ) => Prisma.PrismaPromise<Array<Prisma.CategoryGetPayload<T>>>;

  findUnique: <T extends Prisma.CategoryFindUniqueArgs>(
    args: Prisma.SelectSubset<T, Prisma.CategoryFindUniqueArgs>
  ) => Prisma.PrismaPromise<Prisma.CategoryGetPayload<T> | null>;
}

export interface TicketOperations {
  create: <T extends Prisma.TicketCreateArgs>(
    args: Prisma.SelectSubset<T, Prisma.TicketCreateArgs>
  ) => Prisma.PrismaPromise<Prisma.TicketGetPayload<T>>;

  findMany: <T extends Prisma.TicketFindManyArgs>(
    args?: Prisma.SelectSubset<T, Prisma.TicketFindManyArgs>
  ) => Prisma.PrismaPromise<Array<Prisma.TicketGetPayload<T>>>;

  findUnique: <T extends Prisma.TicketFindUniqueArgs>(
    args: Prisma.SelectSubset<T, Prisma.TicketFindUniqueArgs>
  ) => Prisma.PrismaPromise<Prisma.TicketGetPayload<T> | null>;

  update: <T extends Prisma.TicketUpdateArgs>(
    args: Prisma.SelectSubset<T, Prisma.TicketUpdateArgs>
  ) => Prisma.PrismaPromise<Prisma.TicketGetPayload<T>>;

  delete: <T extends Prisma.TicketDeleteArgs>(
    args: Prisma.SelectSubset<T, Prisma.TicketDeleteArgs>
  ) => Prisma.PrismaPromise<Prisma.TicketGetPayload<T>>;
}

export interface TransactionClient {
  category: CategoryOperations;
  ticket: TicketOperations;
}
