import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {

  IsOptional,
  IsString,
  IsBoolean,
  IsEnum,
  IsArray,
  IsDateString,

  IsN<PERSON><PERSON>,
  Min,
  <PERSON>,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { CustomerStatus, CustomerType } from '@prisma/client';

/**
 * Common customer response DTO used across all modules
 */
export class CustomerResponseDto {
  @ApiProperty({ description: 'Customer ID' })
  id: string;

  @ApiPropertyOptional({ description: 'External system ID' })
  externalId?: string;

  @ApiProperty({ description: 'Customer first name' })
  firstName: string;

  @ApiProperty({ description: 'Customer last name' })
  lastName: string;

  @ApiProperty({ description: 'Customer email address' })
  email: string;

  @ApiPropertyOptional({ description: 'Customer phone number' })
  phone?: string;

  @ApiPropertyOptional({ description: 'Customer date of birth' })
  dateOfBirth?: Date;

  @ApiPropertyOptional({ description: 'Company name for business customers' })
  companyName?: string;

  @ApiPropertyOptional({ description: 'Tax identification number' })
  taxId?: string;

  @ApiProperty({ 
    description: 'Customer status',
    enum: CustomerStatus,
    example: CustomerStatus.ACTIVE
  })
  status: CustomerStatus;

  @ApiProperty({ 
    description: 'Customer type',
    enum: CustomerType,
    example: CustomerType.INDIVIDUAL
  })
  type: CustomerType;

  @ApiProperty({ description: 'Whether email is verified' })
  isEmailVerified: boolean;

  @ApiProperty({ description: 'Whether phone is verified' })
  isPhoneVerified: boolean;

  @ApiProperty({ description: 'Whether KYC is verified' })
  isKycVerified: boolean;

  @ApiPropertyOptional({ description: 'Customer tags', type: [String] })
  tags?: string[];

  @ApiPropertyOptional({ description: 'Additional notes' })
  notes?: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;

  @ApiPropertyOptional({ description: 'Deletion timestamp (soft delete)' })
  deletedAt?: Date;
}

/**
 * Pagination DTO used across all modules
 */
export class PaginationDto {
  @ApiPropertyOptional({ 
    description: 'Number of records to skip',
    minimum: 0,
    default: 0
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseInt(value, 10))
  skip?: number = 0;

  @ApiPropertyOptional({ 
    description: 'Number of records to take',
    minimum: 1,
    maximum: 100,
    default: 20
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Transform(({ value }) => parseInt(value, 10))
  take?: number = 20;

  @ApiPropertyOptional({ 
    description: 'Field to sort by',
    example: 'createdAt'
  })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiPropertyOptional({ 
    description: 'Sort order',
    enum: ['asc', 'desc'],
    default: 'desc'
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'desc';
}

/**
 * Customer list response DTO used across all modules
 */
export class CustomerListResponseDto {
  @ApiProperty({ description: 'List of customers', type: [CustomerResponseDto] })
  data: CustomerResponseDto[];

  @ApiProperty({ description: 'Total number of customers' })
  total: number;

  @ApiProperty({ description: 'Number of records skipped' })
  skip: number;

  @ApiProperty({ description: 'Number of records taken' })
  take: number;
}

/**
 * Customer filter DTO used for search and filtering
 */
export class CustomerFilterDto {
  @ApiPropertyOptional({ description: 'Search term for name, email, company, phone, or tax ID' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by customer status',
    enum: CustomerStatus
  })
  @IsOptional()
  @IsEnum(CustomerStatus)
  status?: CustomerStatus;

  @ApiPropertyOptional({ 
    description: 'Filter by customer type',
    enum: CustomerType
  })
  @IsOptional()
  @IsEnum(CustomerType)
  type?: CustomerType;

  @ApiPropertyOptional({ description: 'Filter by email verification status' })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  isEmailVerified?: boolean;

  @ApiPropertyOptional({ description: 'Filter by phone verification status' })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  isPhoneVerified?: boolean;

  @ApiPropertyOptional({ description: 'Filter by KYC verification status' })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  isKycVerified?: boolean;

  @ApiPropertyOptional({ description: 'Filter by customer tags', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: 'Filter customers created after this date' })
  @IsOptional()
  @IsDateString()
  @Transform(({ value }) => value ? new Date(value) : undefined)
  createdAfter?: Date;

  @ApiPropertyOptional({ description: 'Filter customers created before this date' })
  @IsOptional()
  @IsDateString()
  @Transform(({ value }) => value ? new Date(value) : undefined)
  createdBefore?: Date;

  @ApiPropertyOptional({ description: 'Filter customers updated after this date' })
  @IsOptional()
  @IsDateString()
  @Transform(({ value }) => value ? new Date(value) : undefined)
  updatedAfter?: Date;

  @ApiPropertyOptional({ description: 'Filter customers updated before this date' })
  @IsOptional()
  @IsDateString()
  @Transform(({ value }) => value ? new Date(value) : undefined)
  updatedBefore?: Date;
}
