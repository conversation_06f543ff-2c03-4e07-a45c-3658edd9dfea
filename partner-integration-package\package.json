{"name": "@ngnair/partner-integration-package", "version": "1.0.0", "description": "Complete partner integration module package for NestJS applications with partner API calls, scope resolution, and user management", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "prepublishOnly": "npm run build"}, "keywords": ["<PERSON><PERSON><PERSON>", "partner-integration", "partner-api", "scope-resolution", "user-scopes", "partner-management", "ngnair"], "author": "NGNAIR Development Team", "license": "MIT", "peerDependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/axios": "^3.0.0", "@nestjs/platform-fastify": "^10.0.0", "rxjs": "^7.8.0", "reflect-metadata": "^0.1.13"}, "dependencies": {"axios": "^1.5.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "files": ["dist/**/*", "src/**/*", "config/**/*", "examples/**/*", "docs/**/*", "README.md"], "repository": {"type": "git", "url": "https://github.com/ngnair/partner-integration-package.git"}, "bugs": {"url": "https://github.com/ngnair/partner-integration-package/issues"}, "homepage": "https://github.com/ngnair/partner-integration-package#readme"}