import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { TransactionController } from './transaction.controller';
import { TransactionService } from './transaction.service';
import { TransactionResolver } from './transaction.resolver';
import { AuthSharedModule } from '../auth-shared/auth-shared.module';

@Module({
  imports: [
    HttpModule,
    AuthSharedModule,
  ],
  controllers: [TransactionController],
  providers: [TransactionService, TransactionResolver],
  exports: [TransactionService],
})
export class TransactionModule {}
