#!/usr/bin/env node

const https = require('https');
const http = require('http');
const { URL } = require('url');

// Configuration
const AUTH_BASE_URL = 'https://ng-auth-dev.dev1.ngnair.com';
const SUPPORT_BASE_URL = 'http://ng-support-local.dev.dev1.ngnair.com:3040';

console.log('🔑 Getting Fresh Authentication Token');
console.log('=====================================');

// Helper function to make HTTPS requests
function makeHttpsRequest(url, method = 'GET', data = null, headers = {}) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const options = {
            hostname: urlObj.hostname,
            port: urlObj.port || 443,
            path: urlObj.pathname + urlObj.search,
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Node.js-Test-Client/1.0',
                ...headers
            }
        };

        if (data) {
            const jsonData = JSON.stringify(data);
            options.headers['Content-Length'] = Buffer.byteLength(jsonData);
        }

        const req = https.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonBody = JSON.parse(body);
                    resolve({
                        success: res.statusCode >= 200 && res.statusCode < 300,
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        data: jsonBody,
                        headers: res.headers,
                        cookies: res.headers['set-cookie'] || []
                    });
                } catch (e) {
                    resolve({
                        success: false,
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        data: body,
                        headers: res.headers,
                        cookies: res.headers['set-cookie'] || [],
                        parseError: e.message
                    });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

// Helper function to make HTTP requests to support backend
function makeHttpRequest(endpoint, method = 'GET', data = null, cookies = '') {
    return new Promise((resolve, reject) => {
        const url = new URL(endpoint, SUPPORT_BASE_URL);
        const options = {
            hostname: url.hostname,
            port: url.port,
            path: url.pathname + url.search,
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Node.js-Test-Client/1.0'
            }
        };

        if (cookies) {
            options.headers['Cookie'] = cookies;
        }

        if (data) {
            const jsonData = JSON.stringify(data);
            options.headers['Content-Length'] = Buffer.byteLength(jsonData);
        }

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonBody = JSON.parse(body);
                    resolve({
                        success: res.statusCode >= 200 && res.statusCode < 300,
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        data: jsonBody,
                        headers: res.headers
                    });
                } catch (e) {
                    resolve({
                        success: false,
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        data: body,
                        headers: res.headers,
                        parseError: e.message
                    });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

// Extract access token from cookies
function extractAccessToken(cookies) {
    for (const cookie of cookies) {
        if (cookie.includes('access_token=')) {
            const match = cookie.match(/access_token=([^;]+)/);
            if (match) {
                return match[1];
            }
        }
    }
    return null;
}

async function testAuthentication() {
    try {
        console.log('\n🔍 Step 1: Testing current token...');
        
        // Test current token first
        const currentToken = 'QTJPVkVRSnArRVlSOHZHZC9yUm5EbzIrOXJ4T2JIaUZzWFhCb1kxV2tlNk9odlBEWjRJRml0S0hBOTk2UkNmQ3RXZlNYMHV0NHRiU1hSRWtzbzFRelRWaVJCdHhmNXR3Tm1IR0pSOGdVWTNWNk9ZZ2picXd5dGRKMTRUQUpqOW1hbTlNR3BFaStEN3c0OGxUVUsvcnVJc1hHR0pvUUljdE1KWWIvVURCVEdoZmlIT3JCN050ZUxYVEUrbXlaTEk1NzdOMGlMdDltRWhMNXQxVldRUW9CbjFzREpRYk16ZlF4UDY4NE9xczlIUWF5VTZLUWxyNVJFaHlhNVNNRlhzRzkvdkx6SmJ4QW5PWEk0cUdMcnp1ak5sRGxiUDc4eVpSR204QmlOZkZBNlY1T0h2T05MZjhFcWUxUHpsZ3B0MzBTb2JyOUxaR2RUYmtaeXl4ZE9rTFNpc2VPYW1zT2JrQjVGRkdpUHUreWdBK3Zna3JzWk9NS0FuN1gwSk9GOFhEeDh0NUlvNjF2Y2VZaW52enk0aE5Td1g2MkRmNmtzS2taTmdQeTJTcDh2WWd2cDZzNG9DNitscERDWEwrTnRVdFdvSzZTZGZKbCtBTEFzaTczNmRXMm5kdk55cjgzV3VOcS93M1NrQWtoclNlVTk3d3dOUXMwVGxrOTVXdHhMb1FxbStDcGE0K0xmSnppNXJxV0JOTEg0NjRQdnY0cndPcUlxcFQ3dDBYcWpTU21xQ1lJazJQenNGbVA2OENlQWhtQWN0RjBZMmRsQXRidXN4MVVOdE4rY1pVKzVxZjRINGg5eGZlL3dKVlo0b2E4cVFXd1dVdzZOa3Z4ZHJRZGV1cHFpVXBVWE9IVWJPMEREMlFWN3R0SDFRTkdWbjNva3hhRkhzVXljVGk3VUZRQmoycnBFU3VEdVorVytSMDZzUmNDa0YvOEUza1pOMjJuMXVTeHFsTmZOc2NYL2RQUGNkWVNVeStEaThFdGxhU2N0N2s0bTlERzR5bCttbXBEaUdab1hGYlNTNVZ5dHRYMzNLR3ZydVNlaDhwY1Y4cWYzelMyeTdrNkNaY1k5dzJ3Q3dic044V29abG0vaHVibDBZdjVmS2R4cThhNXBIQzVMVERXSWJQVVpSRWFVUGtpVEJFcjMvZmpSQUNVbmJTQStnWS9lSDNIeUF0SC84Ykp4dS8yS0F3K2ZXQitiVVduUEh1Wk5vdkZDU21jcTNDeGtyZklZek9tUk1XU0ZFdFZZOEdlVm8xRXFUY1VZZjhvVDZYbU5qQ0tFeHM3UHZWSk1HS0tZRG52NXByeWxFVk1vQkZoczZtNWtRenk1MEI1TCtDZHZlM2tkb3JOL2QrdjFOcjA2cWRGcHVQaktwVnRaeGM5Umw3MzltY3BWc3NkdThMN25mTTFhczZaMm5IQWFkYktldW1wY2orL1NZMXRYNENUOTllTWJaeWMxdnBvNi9oZWpCRkNQLytPaXd1OVd1RzFETTU0UT09LS1nOXZQRTFOMUVkUitPS2VMLS0wMTBaMStmZDRMVkJqdlFZcTNoVnZ3PT0%3D';
        
        const testResult = await makeHttpRequest('/partner-test/debug-auth', 'GET', null, `access_token=${currentToken}`);
        
        if (testResult.success) {
            console.log('✅ Current token works! No need to get a fresh one.');
            console.log('Response:', JSON.stringify(testResult.data, null, 2));
            return currentToken;
        } else {
            console.log(`❌ Current token failed: ${testResult.status} - ${testResult.data.message || testResult.data}`);
        }

        console.log('\n🔑 Step 2: Attempting to get fresh token...');
        console.log('Note: This requires valid credentials which may not be available in this test environment.');
        
        // Try to get a fresh token (this would normally require login credentials)
        // For now, let's try to see what endpoints are available
        const authInfoResult = await makeHttpsRequest(`${AUTH_BASE_URL}/api/v1/health`);
        console.log('Auth service health check:', authInfoResult.status);
        
        if (authInfoResult.success) {
            console.log('✅ Auth service is accessible');
        } else {
            console.log('❌ Auth service is not accessible');
        }

        console.log('\n🔧 Step 3: Analyzing the token decryption issue...');
        console.log('The current token appears to be encrypted with a different key or method.');
        console.log('Possible solutions:');
        console.log('1. Check if the ACCESS_TOKEN_ENCRYPTION_KEY matches the one used by the auth service');
        console.log('2. Verify the encryption method (AES-256-GCM) is correct');
        console.log('3. Check if the token format has changed');
        console.log('4. Get a fresh token from the auth service with the correct encryption');

        return null;

    } catch (error) {
        console.error('❌ Authentication test failed:', error.message);
        return null;
    }
}

// Run the test
testAuthentication().then((token) => {
    if (token) {
        console.log('\n✅ Authentication successful!');
        console.log(`Token: ${token.substring(0, 50)}...`);
    } else {
        console.log('\n❌ Authentication failed - token decryption issue needs to be resolved');
    }
}).catch(console.error);
