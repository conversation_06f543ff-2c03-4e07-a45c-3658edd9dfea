/**
 * Partner Integration Types
 * Defines all types for partner API integration
 */

export interface PartnerConfig {
  /**
   * Base URL for the partner API
   * @example 'https://ng-partner-dev.dev1.ngnair.com/api/v1'
   */
  partnerApiBaseUrl: string;

  /**
   * Request timeout in milliseconds
   * @default 10000
   */
  requestTimeout?: number;

  /**
   * Default headers to include with requests
   */
  defaultHeaders?: Record<string, string>;

  /**
   * Enable debug logging
   * @default false
   */
  enableDebugLogging?: boolean;
}

export interface PartnerInfo {
  id: number;
  public_uid: string;
  business_name: string;
  email: string;
  phone?: string;
  platform_fee: string;
  platform_fee_type: string;
  address?: {
    zip: string;
    city: string;
    state: string;
    street: string;
    country: string;
  };
  created_at: string;
  updated_at: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  suffix?: string;
  status: string;
  industries?: any[];
}

export interface UserScopes {
  user_id: string;
  partner_id: string;
  scopes: string[];
}

export interface PartnerApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface GetUserScopesParams {
  partnerId: string;
  userId: string;
  tenantType?: 'partner' | 'account';
}

export interface GetPartnerInfoParams {
  partnerId: string;
}

export interface PartnerListResponse {
  partners: PartnerInfo[];
  total: number;
}

export enum TenantType {
  PARTNER = 'partner',
  ACCOUNT = 'account',
}

export interface ScopeContext {
  userId: string;
  partnerId: string;
  tenantType: TenantType;
  scopes: string[];
  partnerInfo?: PartnerInfo;
}

/**
 * Default configuration values
 */
export const DEFAULT_PARTNER_CONFIG: Partial<PartnerConfig> = {
  requestTimeout: 10000,
  enableDebugLogging: false,
  defaultHeaders: {
    'Content-Type': 'application/json',
    'User-Agent': 'Partner-Integration-Package/1.0',
  },
};
