import { Prisma } from "@prisma/client";

export const categorySelect = {
  id: true,
  name: true,
  description: true
} as const;

export type CategorySelect = typeof categorySelect;
export type CategoryPayload = Prisma.CategoryGetPayload<{ select: CategorySelect }>;

export const commentSelect = {
  id: true,
  message: true,
  authorId: true,
  ticketId: true,
  createdAt: true,
  updatedAt: true
} as const;

export const fileSelect = {
  id: true,
  filename: true,
  fileSize: true,
  fileUrl: true,
  mimeType: true,
  uploadedBy: true,
  uploadedAt: true
} as const;

export const ticketInclude = {
  category: {
    select: categorySelect
  },
  comments: {
    select: {
      id: true,
      message: true,
      authorId: true,
      ticketId: true,
      createdAt: true,
      updatedAt: true
    },
    orderBy: { createdAt: "desc" as const }
  },
  files: {
    select: fileSelect,
    orderBy: { uploadedAt: "desc" as const }
  }
} as const;

export const defaultTicketSelect = {
  id: true,
  subject: true,
  description: true,
  status: true,
  priority: true,
  accountId: true,
  partnerId: true,
  assignedTo: true,
  createdBy: true,
  lastUpdatedBy: true,
  firstName: true,
  lastName: true,
  email: true,
  partnerUserId: true,
  partnerRole: true,
  partnerOrgId: true,
  entSet: true,
  createdAt: true,
  updatedAt: true
} as const;

export type TicketSelect = typeof defaultTicketSelect;

export const ticketSelect = {
  ...defaultTicketSelect,
  category: {
    select: categorySelect
  },
  comments: {
    select: {
      id: true,
      message: true,
      authorId: true,
      ticketId: true,
      createdAt: true,
      updatedAt: true
    },
    orderBy: { createdAt: "desc" as const }
  },
  files: {
    select: fileSelect,
    orderBy: { uploadedAt: "desc" as const }
  }
} as const;

export type TicketPayload = Prisma.TicketGetPayload<{
  select: typeof ticketSelect;
}>;
