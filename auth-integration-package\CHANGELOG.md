# Changelog

All notable changes to the `@ngnair/auth-integration-package` will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2.0.0] - 2025-09-01

### 🚀 Major Updates - Authentication Flow Corrections

This release includes comprehensive improvements to match the working implementation developed during extensive testing and debugging.

### ✅ Added

#### **Corrected JWT Authentication Flow**
- Implemented exact 4-step authentication sequence matching Ruby implementation:
  1. Extract and URL decode access_token from HTTP cookies
  2. Base64 decode to get Rails MessageEncryptor format (`encrypted_data--iv--auth_tag`)
  3. Decrypt using AES-256-GCM with configured encryption key
  4. Verify and decode JWT payload returning only actual JWT fields

#### **Rails MessageEncryptor Compatibility**
- Full support for Rails MessageEncryptor format with proper parsing
- Correct handling of `encrypted_data--iv--auth_tag` structure
- Proper AES-256-GCM decryption with authentication tag validation
- URL decoding and Base64 decoding in correct sequence

#### **JWT Payload Structure Corrections**
- Returns **ONLY actual JWT fields** from the decrypted token
- Removed hardcoded `role` and `permissions` fields that don't exist in JWT
- Removed computed fields like `createdAt`, `updatedAt`, and `expiresAt`
- Matches Ruby JWT payload structure exactly with fields:
  - `iss`, `sub`, `aud`, `exp`, `iat`, `jti` (standard JWT fields)
  - `sid`, `azp`, `ent_set`, `perm_v`, `amr`, `auth_time` (NGNair-specific fields)
  - `email` (optional field, may not be present in all JWTs)

### 🔧 Changed

#### **Authentication Service (`AuthService`)**
- **`decryptCookie()` method**: Completely rewritten to follow exact Rails MessageEncryptor format
- **`verifyToken()` method**: Updated to handle JWT payload extraction correctly
- **`getUserFromPayload()` method**: Now returns only actual JWT fields (no computed fields)
- **`authenticateUserFromCookies()` method**: Updated return type and improved error handling

#### **Authentication Controller (`AuthController`)**
- **`/auth/me` endpoint**: Updated to return actual JWT payload fields only
- **Swagger documentation**: Updated to reflect correct response structure
- **Response type**: Changed from `User` to `any` to reflect actual JWT payload

#### **Type Definitions**
- **`JWTPayload` interface**: Updated to match Ruby JWT structure exactly
- Added comprehensive documentation for each field
- Marked legacy fields as deprecated for backward compatibility

### 🐛 Fixed

#### **Critical Authentication Issues**
- **Token Decryption**: Fixed Rails MessageEncryptor format parsing
- **JWT Payload Extraction**: Removed hardcoded and computed fields
- **Cookie Processing**: Corrected URL decoding and Base64 decoding sequence
- **External Service Integration**: Fixed cookie forwarding mechanism

### ⚠️ Breaking Changes

#### **Response Structure Changes**
- **`/auth/me` endpoint**: Now returns actual JWT payload instead of computed User object
- **Return types**: Changed from `User` to `any` for JWT payload methods
- **Field removal**: Removed hardcoded `role`, `permissions`, and computed timestamp fields

### 🔄 Migration Guide

#### **For `/auth/me` endpoint consumers:**

**Before (v1.x):**
```json
{
  "id": "user-id",
  "email": "<EMAIL>",
  "role": "user",
  "permissions": []
}
```

**After (v2.0):**
```json
{
  "iss": "https://ng-auth-dev.dev1.ngnair.com",
  "sub": "f7b98e6f-95af-4d54-9c53-312ada49ba6e",
  "aud": "ngnair",
  "exp": 1756774160,
  "iat": 1756687760,
  "jti": "1af073f4-71e4-4aca-99b9-f41f92770a0a",
  "sid": "sess_35ee063223007077",
  "azp": "webapp",
  "ent_set": "de2e18a49c8b",
  "perm_v": 0,
  "amr": ["pwd"],
  "auth_time": 1756687760
}
```

## [1.0.0] - 2025-08-31

### Added
- Initial release of the authentication integration package
- Rails-compatible AES-256-GCM cookie decryption
- Local JWT processing for `/auth/me` endpoint
- External service forwarding for `/auth/users` endpoints
- Comprehensive TypeScript support
- Authentication guards (AuthGuard, OptionalAuthGuard)
- Flexible configuration options (environment variables, programmatic, async)
- Complete documentation and examples
- Support for JWKS-based JWT verification
- Detailed logging and debugging capabilities

### Features
- **AuthModule**: Main module with multiple registration options
- **AuthService**: Core service handling encryption, JWT processing, and external API calls
- **AuthController**: Pre-built controller with authentication endpoints
- **AuthGuard**: Route protection guard
- **OptionalAuthGuard**: Optional authentication guard
- **Type Definitions**: Complete TypeScript interfaces and types
- **Configuration**: Flexible configuration with environment variable support

### Endpoints Provided
- `GET /auth/me` - Get current user (local JWT processing, no external calls)
- `GET /auth/users` - Get all users (external service proxy)
- `GET /auth/users/:id` - Get user by ID (external service proxy)

### Documentation
- Complete README with installation and usage instructions
- Integration guide with step-by-step setup
- Basic and advanced integration examples
- Configuration reference
- Troubleshooting guide

### Security Features
- AES-256-GCM encryption/decryption
- JWT verification with JWKS support
- Secure cookie handling
- Request timeout protection
- Comprehensive error handling

### Development Features
- TypeScript support with full type definitions
- Debug logging capabilities
- Development mode with optional JWT verification
- Comprehensive error messages
- Performance optimizations

## [Unreleased]

### Planned Features
- Redis-based session storage
- Rate limiting middleware
- Role-based access control decorators
- Refresh token handling
- Multi-tenant support
- Metrics and monitoring integration
- Additional authentication providers
- Session management utilities

### Potential Improvements
- Performance optimizations for large-scale deployments
- Additional configuration validation
- Enhanced error reporting
- Automated testing suite
- CI/CD pipeline integration
- Docker containerization examples
