import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { createHash } from 'crypto';
import { PrismaService } from '../../prisma/prisma.service';
import { NearPayWebhookDto, PaymentTokenResponseDto } from './dto/nearpay-webhook.dto';
import { PaymentTokenStatus, PaymentTokenType, PaymentProvider } from '@prisma/client';

@Injectable()
export class PaymentTokensService {
  private readonly logger = new Logger(PaymentTokensService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Process NearPay webhook and create/update payment token
   */
  async processNearPayWebhook(webhookData: NearPayWebhookDto, clientIp?: string): Promise<PaymentTokenResponseDto> {
    this.logger.log(`Processing NearPay webhook for transaction: ${webhookData.transactionId}`);

    try {
      // Find customer by email, phone, or customerId
      const customer = await this.findCustomerFromWebhook(webhookData);
      if (!customer) {
        throw new NotFoundException('Customer not found for payment token creation');
      }

      // Only process approved transactions for token creation
      if (webhookData.status !== 'APPROVED') {
        this.logger.warn(`Skipping token creation for non-approved transaction: ${webhookData.transactionId}`);
        throw new BadRequestException('Only approved transactions can create payment tokens');
      }

      // Create token hash from card data
      const tokenData = `${customer.id}:${webhookData.card.last4}:${webhookData.card.brand}:${webhookData.merchant.merchantId}`;
      const tokenHash = this.createTokenHash(tokenData);

      // Check if token already exists
      let paymentToken = await this.prisma.paymentToken.findUnique({
        where: { tokenHash },
        include: { customer: true },
      });

      if (paymentToken) {
        // Update existing token
        paymentToken = await this.prisma.paymentToken.update({
          where: { id: paymentToken.id },
          data: {
            lastUsedAt: new Date(),
            usageCount: { increment: 1 },
            rawWebhookData: {
              transactionId: webhookData.transactionId,
              amount: webhookData.amount,
              currency: webhookData.currency,
              merchantId: webhookData.merchant.merchantId,
              terminalId: webhookData.merchant.terminalId,
              cardType: webhookData.card.cardType,
              last4: webhookData.card.last4,
              authCode: webhookData.authCode,
              timestamp: webhookData.timestamp,
            },
          },
          include: { customer: true },
        });
        
        this.logger.log(`Updated existing payment token: ${paymentToken.id}`);
      } else {
        // Create new token
        paymentToken = await this.prisma.paymentToken.create({
          data: {
            customerId: customer.id,
            tokenHash,
            externalTokenId: webhookData.transactionId,
            paymentProvider: PaymentProvider.NEARPAY,
            tokenType: PaymentTokenType.TAP_TO_PAY,
            status: PaymentTokenStatus.ACTIVE,
            maskedInfo: `**** **** **** ${webhookData.card.last4}`,
            paymentBrand: webhookData.card.brand,
            createdByIp: clientIp,
            lastUsedAt: new Date(),
            usageCount: 1,
            providerMetadata: {
              cardType: webhookData.card.cardType,
              merchantName: webhookData.merchant.merchantName,
              terminalId: webhookData.merchant.terminalId,
            },
            rawWebhookData: {
              transactionId: webhookData.transactionId,
              amount: webhookData.amount,
              currency: webhookData.currency,
              merchantId: webhookData.merchant.merchantId,
              terminalId: webhookData.merchant.terminalId,
              cardType: webhookData.card.cardType,
              last4: webhookData.card.last4,
              authCode: webhookData.authCode,
              timestamp: webhookData.timestamp,
            },
          },
          include: { customer: true },
        });

        this.logger.log(`Created new payment token: ${paymentToken?.id} for customer: ${customer.id}`);
      }

      return this.mapToResponseDto(paymentToken);
    } catch (error) {
      this.logger.error(`Failed to process NearPay webhook: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get payment tokens for a customer
   */
  async getCustomerPaymentTokens(customerId: string): Promise<PaymentTokenResponseDto[]> {
    const tokens = await this.prisma.paymentToken.findMany({
      where: { 
        customerId,
        deletedAt: null,
        status: PaymentTokenStatus.ACTIVE,
      },
      orderBy: { lastUsedAt: 'desc' },
      include: { customer: true },
    });

    return tokens.map(token => this.mapToResponseDto(token));
  }

  /**
   * Revoke a payment token
   */
  async revokePaymentToken(tokenId: string, customerId: string): Promise<void> {
    const token = await this.prisma.paymentToken.findFirst({
      where: { id: tokenId, customerId },
    });

    if (!token) {
      throw new NotFoundException('Payment token not found');
    }

    await this.prisma.paymentToken.update({
      where: { id: tokenId },
      data: { status: PaymentTokenStatus.REVOKED },
    });

    this.logger.log(`Revoked payment token: ${tokenId} for customer: ${customerId}`);
  }

  /**
   * Find customer from webhook data
   */
  private async findCustomerFromWebhook(webhookData: NearPayWebhookDto) {
    let customer = null;

    // Try to find by customerId first
    if (webhookData.customerId) {
      customer = await this.prisma.customer.findUnique({
        where: { id: webhookData.customerId },
      });
    }

    // Try to find by email
    if (!customer && webhookData.customerEmail) {
      customer = await this.prisma.customer.findUnique({
        where: { email: webhookData.customerEmail },
      });
    }

    // Try to find by phone
    if (!customer && webhookData.customerPhone) {
      customer = await this.prisma.customer.findFirst({
        where: { phone: webhookData.customerPhone },
      });
    }

    return customer;
  }

  /**
   * Create secure hash for token identification
   */
  private createTokenHash(data: string): string {
    return createHash('sha256').update(data).digest('hex');
  }

  /**
   * Map entity to response DTO
   */
  private mapToResponseDto(token: any): PaymentTokenResponseDto {
    return {
      id: token.id,
      customerId: token.customerId,
      paymentProvider: token.paymentProvider,
      tokenType: token.tokenType,
      status: token.status,
      maskedInfo: token.maskedInfo,
      paymentBrand: token.paymentBrand,
      expiresAt: token.expiresAt,
      lastUsedAt: token.lastUsedAt,
      usageCount: token.usageCount,
      createdAt: token.createdAt,
    };
  }
}
