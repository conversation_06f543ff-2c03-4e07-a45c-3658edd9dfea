import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { AuthSharedService, ApiGuard, AdminGuard } from '../auth-shared';
import { ConfigModule } from '../../config/config.module';

@Module({
  imports: [ConfigModule, HttpModule],
  providers: [
    AuthSharedService,
    ApiGuard,
    AdminGuard,
  ],
  exports: [
    AuthSharedService,
    ApiGuard,
    AdminGuard,
  ],
})
export class SharedGuardsModule {}
