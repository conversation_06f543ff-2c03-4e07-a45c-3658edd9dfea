import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { AppController } from "./app.controller";
import { ConfigModule } from "./config/config.module";
import { AuthSharedModule } from "./modules/auth-shared/auth-shared.module";
import { HealthModule } from "./modules/health";
import { WebhookModule } from "./webhook/webhook.module";
import { GraphQLConfigModule } from "./modules/graphql/graphql.module";
import { PrismaModule } from "./prisma/prisma.module";
import { CustomersQueryModule } from "./modules/customers-query/customers-query.module";
import { CustomersManagementModule } from "./modules/customers-management/customers-management.module";
import { CustomersVerificationModule } from "./modules/customers-verification/customers-verification.module";
import { CustomersAdminModule } from "./modules/customers-admin/customers-admin.module";
import { CustomersAuditModule } from "./modules/customers-audit/customers-audit.module";
import { CustomersAccessControlModule } from "./modules/customers-access-control/customers-access-control.module";
import { CustomersSharedModule } from "./modules/customers-shared/customers-shared.module";
import { TransactionModule } from "./modules/transactions/transaction.module";
import { PaymentTokensModule } from "./modules/payment-tokens/payment-tokens.module";

@Module({
  imports: [
    ConfigModule,
    PrismaModule,
    GraphQLConfigModule,
    CustomersQueryModule,
    CustomersManagementModule,
    CustomersVerificationModule,
    CustomersAdminModule,
    CustomersAuditModule,
    CustomersAccessControlModule,
    CustomersSharedModule,
    TransactionModule,
    PaymentTokensModule,
    WebhookModule,
    AuthSharedModule,
    HttpModule,
    HealthModule,
  ],
  controllers: [AppController],
  providers: [],
})
export class AppModule {}
