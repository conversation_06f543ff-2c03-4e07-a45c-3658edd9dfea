import {
  <PERSON>,
  <PERSON>,
  Param,
  Logger,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
} from '@nestjs/swagger';
import { CustomersVerificationService, CustomerWithRelations } from './customers-verification.service';
import { CustomerResponseDto } from '../customers-shared/dto/customer-shared.dto';

@ApiTags('customers-verification')
@Controller('customers')
export class CustomersVerificationController {
  private readonly logger = new Logger(CustomersVerificationController.name);

  constructor(private readonly customersVerificationService: CustomersVerificationService) {}

  /**
   * Convert CustomerWithRelations to CustomerResponseDto
   */
  private mapToResponseDto(customer: CustomerWithRelations): CustomerResponseDto {
    return {
      ...customer,
      externalId: customer.externalId || undefined,
    } as CustomerResponseDto;
  }

  @Post(':id/verify-email')
  @ApiOperation({ 
    summary: 'Verify customer email address',
    description: 'Mark a customer\'s email address as verified. This is typically called after the customer has completed email verification process.'
  })
  @ApiParam({ name: 'id', description: 'Customer unique identifier (UUID)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Email verified successfully',
    type: CustomerResponseDto,
    examples: {
      success: {
        summary: 'Successful email verification',
        value: {
          id: 'cmca6q7w40000l9015jo4lc5o',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          isEmailVerified: true,
          isKycVerified: false,
          status: 'ACTIVE',
          verifiedAt: '2024-01-15T14:30:00Z',
          updatedAt: '2024-01-15T14:30:00Z'
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Customer not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Email already verified or customer not eligible for verification',
  })
  async verifyEmail(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<CustomerResponseDto> {
    this.logger.log(`Verifying email for customer ${id}`);
    const customer = await this.customersVerificationService.verifyEmail(id);
    return this.mapToResponseDto(customer);
  }

  @Post(':id/verify-phone')
  @ApiOperation({ 
    summary: 'Verify customer phone number',
    description: 'Mark a customer\'s phone number as verified. This is typically called after the customer has completed phone verification process (e.g., SMS OTP).'
  })
  @ApiParam({ name: 'id', description: 'Customer unique identifier (UUID)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Phone verified successfully',
    type: CustomerResponseDto,
    examples: {
      success: {
        summary: 'Successful phone verification',
        value: {
          id: 'cmca6q7w40000l9015jo4lc5o',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          phone: '+1234567890',
          isEmailVerified: true,
          isPhoneVerified: true,
          isKycVerified: false,
          status: 'ACTIVE',
          verifiedAt: '2024-01-15T14:30:00Z',
          updatedAt: '2024-01-15T14:35:00Z'
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Customer not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Phone already verified or customer not eligible for verification',
  })
  async verifyPhone(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<CustomerResponseDto> {
    this.logger.log(`Verifying phone for customer ${id}`);
    const customer = await this.customersVerificationService.verifyPhone(id);
    return this.mapToResponseDto(customer);
  }

  @Post(':id/verify-kyc')
  @ApiOperation({ 
    summary: 'Verify customer KYC (Know Your Customer)',
    description: 'Mark a customer as KYC verified after completing identity verification process. This typically involves document verification and identity checks.'
  })
  @ApiParam({ name: 'id', description: 'Customer unique identifier (UUID)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'KYC verified successfully',
    type: CustomerResponseDto,
    examples: {
      success: {
        summary: 'Successful KYC verification',
        value: {
          id: 'cmca6q7w40000l9015jo4lc5o',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          isEmailVerified: true,
          isPhoneVerified: true,
          isKycVerified: true,
          status: 'ACTIVE',
          kycVerifiedAt: '2024-01-15T15:00:00Z',
          updatedAt: '2024-01-15T15:00:00Z'
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Customer not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'KYC already verified or customer not eligible for KYC verification',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions to verify KYC',
  })
  async verifyKyc(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<CustomerResponseDto> {
    this.logger.log(`Verifying KYC for customer ${id}`);
    const customer = await this.customersVerificationService.verifyKyc(id);
    return this.mapToResponseDto(customer);
  }
}
