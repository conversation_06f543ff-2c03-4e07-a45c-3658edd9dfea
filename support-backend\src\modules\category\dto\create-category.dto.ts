import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsArray, IsN<PERSON>ber, <PERSON>, Max } from 'class-validator';
import { CategoryType } from '@prisma/client';
import { InputType, Field, registerEnumType } from '@nestjs/graphql';

registerEnumType(CategoryType, {
  name: 'CategoryType',
  description: 'The type of the category (internal, partner, software_partner)',
});

@InputType()
export class CreateCategoryDto {
  @Field()
  @ApiProperty({ description: 'Category name' })
  @IsString()
  name: string;

  @Field({ nullable: true })
  @ApiProperty({ description: 'Category description', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @Field(() => CategoryType, { nullable: true })
  @ApiProperty({ 
    description: 'Category type', 
    enum: CategoryType,
    default: CategoryType.internal
  })
  @IsOptional()
  @IsEnum(CategoryType)
  type?: CategoryType;

  @Field(() => [String], { nullable: 'itemsAndList' })
  @ApiProperty({ 
    description: 'Auto-assign tickets to these IDs (partnerOrgId, softwarePartnerId, teamId)', 
    type: [String],
    required: false 
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  autoAssignTo?: string[];

  @Field({ nullable: true, defaultValue: 60 })
  @ApiProperty({ 
    description: 'Timeout in minutes before escalation',
    minimum: 1,
    maximum: 10080, // 1 week
    default: 60,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10080)
  timeoutMinutes?: number;

  @Field({ nullable: true })
  @ApiProperty({ 
    description: 'ID to escalate to (teamId, supportAdminId, or partnerOrgId)',
    required: false 
  })
  @IsOptional()
  @IsString()
  escalateTo?: string;
}
