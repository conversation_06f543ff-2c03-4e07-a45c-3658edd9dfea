// src/main.ts
import { NestFactory } from '@nestjs/core';
import {
  FastifyAdapter,
  NestFastifyApplication,
} from '@nestjs/platform-fastify';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import fastifySwagger from '@fastify/swagger';
import fastifySwaggerUi from '@fastify/swagger-ui';
import Fastify from 'fastify';
import multiPart from '@fastify/multipart';
import helmet from '@fastify/helmet';
const cookie = require('@fastify/cookie');
// import { contentParser } from "fastify-file-interceptor";

async function bootstrap() {
  // For Docker containers, always bind to 0.0.0.0 to accept external connections
  // Check if we're running in Docker by looking for container-specific environment variables
  const isDocker = process.env.HOSTNAME && process.env.HOSTNAME.length === 12 || // Docker container hostname
                   process.env.DOCKER_ENV === 'true' ||
                   process.env.NODE_ENV === 'production' ||
                   process.env.DATABASE_URL?.includes('support-backend-db'); // Docker service name

  const sourceIp = isDocker ? '0.0.0.0' : (process.env.SOURCE_IP || '127.0.0.1');
  
  const fastifyAdapter = new FastifyAdapter({
    logger: true,
  });
  // Register plugins on the Fastify instance used by the adapter
  await (fastifyAdapter.getInstance() as any).register(multiPart);
  await (fastifyAdapter.getInstance() as any).register(cookie, {
    secret: process.env.COOKIE_SECRET || 'default-secret-key-for-development', // for signed cookies
    parseOptions: {
      decode: decodeURIComponent, // Properly decode URL-encoded cookies
      maxAge: 86400000, // 24 hours
      httpOnly: false, // Allow client-side access if needed
      secure: false, // Set to true in production with HTTPS
      sameSite: 'lax' // CSRF protection
    }
  });

  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    fastifyAdapter,
  );

  console.log(app.getHttpAdapter().getType()); // should log "fastify"

  app.useGlobalPipes(new ValidationPipe({ whitelist: true, transform: true }));

  // Define allowed origins based on environment
  const isDevOrTest = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test';
  const allowedOrigins = isDevOrTest
    ? [
        'http://localhost:3041',  // Local frontend development
        'http://localhost:3042',  // Local admin development
        'http://localhost:3040',  // Local GraphQL Playground
        'http://ng-support-fe-local.dev.dev1.ngnair.com:3041',  // Frontend service
        'http://ng-support-admin-local.dev.dev1.ngnair.com:3042',  // Admin service
        'http://ng-support-local.dev.dev1.ngnair.com:3040',  // GraphQL Playground
        'https://ng-auth-dev.dev1.ngnair.com'  // Auth service
      ]
    : [
        process.env.FRONTEND_URL,
        process.env.ADMIN_URL,
        process.env.AUTH_URL,
        process.env.OTHER_URL,
      ].filter((origin): origin is string => Boolean(origin));

  app.enableCors({
    origin: (origin, callback) => {
      // Allow requests without origin (like GraphQL Playground, Postman, curl)
      if (!origin) {
        callback(null, true);
        return;
      }

      // Allow requests from allowed origins
      if (allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        console.log(`CORS blocked origin: ${origin}`);
        callback(new Error('Not allowed by CORS'), false);
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Cookie', 'X-Requested-With'],
  });

  const config = new DocumentBuilder()
    .setTitle('Support Service')
    .setDescription('API for managing Support Tickets and Comments')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'Authorization',
        in: 'header',
      }, // name for reference
    )
    .build();

  if (process.env.NODE_ENV === 'production') {
    // Disable console logs in production
    console.log = () => {};
    console.warn = () => {};
    console.info = () => {};
    console.debug = () => {};
  }

  const documentFactory = () => SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, documentFactory);

  const listen_port = process.env.LISTENING_PORT || process.env.PORT || 3040;
  await app.listen(listen_port, sourceIp);
}
bootstrap();
