import { Controller, Get, Post, Body, Param, Put, Delete, Query, UseGuards, NotFoundException, Request, BadRequestException } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiQuery, ApiResponse, ApiTags } from "@nestjs/swagger";
import { TicketStatus, TicketPriority, Prisma } from "@prisma/client";
import { ApiGuard } from "../auth/src/guards";
import { ScopeGuard, SupportAccess, RequirePermissions } from "../auth/src/guards/scope.guard";
import { AuthenticatedRequest } from "../auth/src/types";
import { ScopeContext } from "../auth/src/types/scope.types";
import { UserDataService } from "../auth/src/user-data.service";

import { CreateTicketDto } from "./dto/create-ticket.dto";
import { UpdateTicketDto } from "./dto/update-ticket.dto";

import { TicketService } from "./ticket.service";
import { TicketPayload } from "./types/ticket.types";

@ApiTags("Tickets")
@Controller("tickets")
@UseGuards(ApiGuard, ScopeGuard)
@SupportAccess()
@ApiBearerAuth()
export class TicketController {
  constructor(
    private readonly ticketService: TicketService,
    private readonly userDataService: UserDataService
  ) {}

  @Post()
  @RequirePermissions('canCreateTickets')
  @ApiOperation({ summary: "Create a new ticket" })
  @ApiResponse({ status: 201, description: "Ticket created successfully" })
  @ApiResponse({ status: 400, description: "Invalid input" })
  @ApiResponse({ status: 404, description: "Category not found" })
  @ApiResponse({ status: 500, description: "Internal server error" })
  async create(
    @Body() createTicketDto: CreateTicketDto,
    @Request() req: AuthenticatedRequest & { scopeContext: ScopeContext }
  ): Promise<TicketPayload> {
    try {
      // Extract user ID from JWT
      const userId = req.user.sub || req.user.id;

      // Extract access token from cookies for user data fetching
      const accessToken = this.userDataService.extractAccessToken((req as any).cookies);

      // Fetch user data from auth API - handle gracefully if it fails
      let userData = null;
      try {
        userData = await this.userDataService.getCachedUserData(userId, accessToken);
        console.log(`✅ [TICKET CONTROLLER] User data fetched successfully for ${userId}:`, userData);
      } catch (userDataError) {
        console.warn(`⚠️ [TICKET CONTROLLER] Failed to fetch user data for ${userId}:`, userDataError.message);
        // Continue with ticket creation even if user data fetch fails
      }

      // Extract partner information from scope context
      const partnerInfo = req.scopeContext.partnerInfo;
      console.log(`🏢 [TICKET CONTROLLER] Partner info from scope context:`, partnerInfo);

      // Prepare ticket data with all available information according to requirements
      const ticketData = {
        ...createTicketDto,
        createdBy: userId,
        lastUpdatedBy: userId,
        // User data fields from /auth/users/{id} endpoint
        firstName: userData?.first_name,
        lastName: userData?.last_name,
        email: userData?.email,
        // Partner information fields as per requirements
        partnerUserId: userId, // Store user's ID (sub from JWT) in partnerUserId field
        partnerRole: req.scopeContext.scope, // Store user's scope in partnerRole field
        partnerOrgId: partnerInfo?.public_uid, // Store partner's public_uid in partnerOrgId field
        entSet: req.scopeContext.tenantType, // Tenant type (partner/account)
      };

      console.log(`📝 [TICKET CONTROLLER] Creating ticket with data:`, ticketData);

      // Use scope-aware creation
      return await this.ticketService.createWithScope(req.scopeContext, ticketData);
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      throw new BadRequestException(
        `An error occurred while creating the ticket: ${errorMessage}`
      );
    }
  }

  @Get()
  @ApiOperation({ summary: "Get all tickets" })
  @ApiQuery({ name: "skip", required: false, type: "number", description: "Number of items to skip" })
  @ApiQuery({ name: "take", required: false, type: "number", description: "Number of items to take" })
  @ApiQuery({ name: "status", required: false, enum: TicketStatus, description: "Filter by ticket status" })
  @ApiQuery({ name: "priority", required: false, enum: TicketPriority, description: "Filter by ticket priority" })
  async findAll(
    @Request() req: AuthenticatedRequest & { scopeContext: ScopeContext },
    @Query("skip") skip?: number,
    @Query("take") take?: number,
    @Query("status") status?: TicketStatus,
    @Query("priority") priority?: TicketPriority
  ): Promise<TicketPayload[]> {
    const where: Prisma.TicketWhereInput = {};
    if (status) {
      where.status = status;
    }
    if (priority) {
      where.priority = priority;
    }

    // Use scope-aware finding with additional filters
    return this.ticketService.findManyWithScope(req.scopeContext, {
      skip,
      take,
      where,
      orderBy: { createdAt: "desc" }
    });
  }

  @Get(":id")
  @ApiOperation({ summary: "Get ticket by id" })
  @ApiResponse({ status: 200, description: "Ticket found" })
  @ApiResponse({ status: 404, description: "Ticket not found" })
  async findOne(
    @Param("id") id: string,
    @Request() req: AuthenticatedRequest & { scopeContext: ScopeContext }
  ): Promise<TicketPayload> {
    // Use scope-aware finding
    const ticket = await this.ticketService.findUniqueWithScope(req.scopeContext, { id });
    if (!ticket) {
      throw new NotFoundException(`Ticket with ID ${id} not found`);
    }
    return ticket;
  }

  @Put(":id")
  @RequirePermissions('canUpdateTickets')
  @ApiOperation({ summary: "Update a ticket" })
  @ApiResponse({ status: 200, description: "Ticket updated successfully" })
  @ApiResponse({ status: 404, description: "Ticket not found" })
  async update(
    @Param("id") id: string,
    @Body() updateTicketDto: UpdateTicketDto,
    @Request() req: AuthenticatedRequest & { scopeContext: ScopeContext }
  ): Promise<TicketPayload> {
    // Use scope-aware update
    return await this.ticketService.updateWithScope(
      req.scopeContext,
      { id },
      {
        ...updateTicketDto,
        lastUpdatedBy: req.user.sub || req.user.id
      }
    );
  }

  @Delete(":id")
  @RequirePermissions('canDeleteTickets')
  @ApiOperation({ summary: "Soft delete a ticket" })
  @ApiResponse({ status: 200, description: "Ticket soft deleted successfully" })
  @ApiResponse({ status: 404, description: "Ticket not found" })
  async remove(
    @Param("id") id: string,
    @Request() req: AuthenticatedRequest & { scopeContext: ScopeContext }
  ): Promise<TicketPayload> {
    // Use scope-aware soft delete
    return await this.ticketService.softDeleteWithScope(req.scopeContext, { id });
  }
}
