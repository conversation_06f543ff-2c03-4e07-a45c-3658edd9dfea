import { PrismaClient, CustomerStatus, CustomerType, AddressType, PaymentTokenStatus, PaymentTokenType, PaymentProvider } from '@prisma/client';
import * as crypto from 'crypto';

// Initialize Prisma Client with robust configuration
const prisma = new PrismaClient({
  log: ['error', 'warn'],
  errorFormat: 'pretty',
});

// Simple data generators without external dependencies
const firstNames = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];

const lastNames = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];

const companyNames = ['Tech Solutions Inc', 'Global Dynamics LLC', 'Innovation Partners', 'Digital Ventures Corp', 'Future Systems Ltd', 'Advanced Technologies', 'Smart Solutions Group', 'NextGen Enterprises', 'Quantum Industries', 'Synergy Corporation', 'Elite Services', 'Premier Solutions', 'Apex Technologies', 'Vertex Systems', 'Pinnacle Group', 'Summit Enterprises', 'Horizon Corp', 'Catalyst Solutions', 'Momentum Inc', 'Velocity Systems'];

const cities = ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia', 'San Antonio', 'San Diego', 'Dallas', 'San Jose', 'Austin', 'Jacksonville', 'Fort Worth', 'Columbus', 'Charlotte', 'San Francisco', 'Indianapolis', 'Seattle', 'Denver', 'Washington', 'Boston', 'Nashville', 'Baltimore', 'Oklahoma City', 'Louisville', 'Portland', 'Las Vegas', 'Milwaukee', 'Albuquerque', 'Tucson'];

const states = ['NY', 'CA', 'IL', 'TX', 'AZ', 'PA', 'FL', 'OH', 'NC', 'WA', 'CO', 'DC', 'MA', 'TN', 'MD', 'OK', 'KY', 'OR', 'NV', 'WI', 'NM'];

const countries = ['US', 'CA', 'GB', 'AU', 'DE', 'FR', 'JP', 'BR'];

const businessTypes = ['Technology', 'Healthcare', 'Finance', 'Retail', 'Manufacturing', 'Consulting', 'Real Estate', 'Education'];

const cardBrands = ['Visa', 'Mastercard', 'American Express', 'Discover'];

// Helper functions
function randomChoice<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function randomFloat(min: number, max: number): number {
  return Math.random() * (max - min) + min;
}

function randomBoolean(probability: number = 0.5): boolean {
  return Math.random() < probability;
}

function generateEmail(firstName: string, lastName: string, index: number, companyName?: string): string {
  const uniqueSuffix = `${index}${randomInt(100, 999)}`;
  if (companyName && randomBoolean(0.7)) {
    const domain = companyName.toLowerCase()
      .replace(/[^a-z0-9]/g, '')
      .substring(0, 10) + '.com';
    return `${firstName.toLowerCase()}.${lastName.toLowerCase()}${uniqueSuffix}@${domain}`;
  }
  const domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'company.com'];
  return `${firstName.toLowerCase()}.${lastName.toLowerCase()}${uniqueSuffix}@${randomChoice(domains)}`;
}

function generatePhone(): string {
  return `+1${randomInt(200, 999)}${randomInt(200, 999)}${randomInt(1000, 9999)}`;
}

function generateDate(minAge: number, maxAge: number): Date {
  const now = new Date();
  const minDate = new Date(now.getFullYear() - maxAge, 0, 1);
  const maxDate = new Date(now.getFullYear() - minAge, 11, 31);
  return new Date(minDate.getTime() + Math.random() * (maxDate.getTime() - minDate.getTime()));
}

function generateTokenHash(tokenData: string): string {
  return crypto.createHash('sha256').update(tokenData).digest('hex');
}

function generateCardNumber(brand: string): string {
  const prefixes: { [key: string]: string } = {
    'Visa': '4',
    'Mastercard': '5',
    'American Express': '3',
    'Discover': '6'
  };
  
  const prefix = prefixes[brand] || '4';
  let cardNumber = prefix;
  
  const remainingLength = brand === 'American Express' ? 14 : 15;
  for (let i = 0; i < remainingLength; i++) {
    cardNumber += randomInt(0, 9).toString();
  }
  
  return cardNumber;
}

function maskCardNumber(cardNumber: string): string {
  if (cardNumber.length === 15) {
    return `**** ****** *${cardNumber.slice(-4)}`;
  }
  return `**** **** **** ${cardNumber.slice(-4)}`;
}

function generateAlphanumeric(length: number): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

function generateNumeric(length: number): string {
  let result = '';
  for (let i = 0; i < length; i++) {
    result += randomInt(0, 9).toString();
  }
  return result;
}

function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

async function main() {
  console.log('🌱 Starting production database seeding...');
  console.log('📊 Configuration: 120 customers with realistic data');

  // Verify database connection and configuration
  try {
    console.log('🔗 Verifying database connection...');
    await prisma.$connect();

    // Test database connectivity
    await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Database connection verified');

    // Display current database configuration (without sensitive data)
    const databaseUrl = process.env.DATABASE_URL || 'Not configured';
    const dbInfo = databaseUrl.replace(/:[^:@]*@/, ':***@'); // Mask password
    console.log(`📍 Database: ${dbInfo}`);

  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    throw new Error('Cannot connect to database. Please check your DATABASE_URL configuration.');
  }

  try {
    // Clear existing data with error handling
    console.log('🧹 Clearing existing data...');

    // Clear in correct order to respect foreign key constraints
    try {
      await prisma.paymentToken.deleteMany();
      console.log('  ✅ Payment tokens cleared');
    } catch (error) {
      console.log('  ⚠️ Payment tokens table not found or already empty');
    }

    try {
      await prisma.auditLog.deleteMany();
      console.log('  ✅ Audit logs cleared');
    } catch (error) {
      console.log('  ⚠️ Audit logs table not found or already empty');
    }

    try {
      await prisma.customerPreference.deleteMany();
      console.log('  ✅ Customer preferences cleared');
    } catch (error) {
      console.log('  ⚠️ Customer preferences table not found or already empty');
    }

    try {
      await prisma.contact.deleteMany();
      console.log('  ✅ Contacts cleared');
    } catch (error) {
      console.log('  ⚠️ Contacts table not found or already empty');
    }

    try {
      await prisma.address.deleteMany();
      console.log('  ✅ Addresses cleared');
    } catch (error) {
      console.log('  ⚠️ Addresses table not found or already empty');
    }

    try {
      await prisma.customer.deleteMany();
      console.log('  ✅ Customers cleared');
    } catch (error) {
      console.log('  ⚠️ Customers table not found or already empty');
    }

    console.log('✅ Data clearing completed');
    
    // Generate customers with all related data
    console.log('👥 Creating customers with addresses and payment methods...');
    
    const customersToCreate = 120;
    
    for (let i = 0; i < customersToCreate; i++) {
      // Determine customer type and business status
      const isBusinessCustomer = randomBoolean(0.3); // 30% business customers
      const customerType = isBusinessCustomer ? 
        randomChoice([CustomerType.BUSINESS, CustomerType.ENTERPRISE]) : 
        CustomerType.INDIVIDUAL;
      
      // Basic customer information
      const firstName = randomChoice(firstNames);
      const lastName = randomChoice(lastNames);
      const companyName = isBusinessCustomer ? randomChoice(companyNames) : null;
      const email = generateEmail(firstName, lastName, i, companyName || undefined);
      const phone = generatePhone();
      const dateOfBirth = generateDate(18, 80);
      
      // Generate realistic status distribution
      const statusWeights = [
        { weight: 70, value: CustomerStatus.ACTIVE },
        { weight: 15, value: CustomerStatus.INACTIVE },
        { weight: 10, value: CustomerStatus.PENDING_VERIFICATION },
        { weight: 5, value: CustomerStatus.SUSPENDED }
      ];
      
      const totalWeight = statusWeights.reduce((sum, item) => sum + item.weight, 0);
      const random = Math.random() * totalWeight;
      let currentWeight = 0;
      let status: CustomerStatus = CustomerStatus.ACTIVE;
      
      for (const item of statusWeights) {
        currentWeight += item.weight;
        if (random <= currentWeight) {
          status = item.value;
          break;
        }
      }

      // Verification flags based on status
      const isEmailVerified = status === CustomerStatus.ACTIVE ? 
        randomBoolean(0.9) : randomBoolean(0.3);
      const isPhoneVerified = status === CustomerStatus.ACTIVE ? 
        randomBoolean(0.8) : randomBoolean(0.2);
      const isKycVerified = status === CustomerStatus.ACTIVE ? 
        randomBoolean(0.7) : false;

      // Business-specific data
      const taxId = isBusinessCustomer ? generateNumeric(9) : null;
      const businessType = isBusinessCustomer ? randomChoice(businessTypes) : null;

      // Generate tags
      const tags = [];
      if (status === CustomerStatus.ACTIVE) tags.push('active');
      if (isKycVerified) tags.push('verified');
      if (isBusinessCustomer) tags.push('business');
      if (customerType === CustomerType.ENTERPRISE) tags.push('enterprise');
      if (randomBoolean(0.2)) tags.push('premium');
      if (randomBoolean(0.1)) tags.push('high-value');

      const notes = randomBoolean(0.6) ? 
        'Generated test customer with realistic profile data' : null;

      // Create customer
      const customer = await prisma.customer.create({
        data: {
          firstName,
          lastName,
          email,
          phone,
          dateOfBirth,
          companyName,
          taxId,
          businessType,
          status,
          type: customerType,
          isEmailVerified,
          isPhoneVerified,
          isKycVerified,
          tags,
          notes,
        },
      });

      // Generate 1-3 addresses per customer
      const numAddresses = randomInt(1, 3);
      
      for (let j = 0; j < numAddresses; j++) {
        const country = randomChoice(countries);
        const addressType = j === 0 ? AddressType.HOME : randomChoice([AddressType.WORK, AddressType.BILLING, AddressType.SHIPPING]);
        
        await prisma.address.create({
          data: {
            customerId: customer.id,
            type: addressType,
            label: addressType === AddressType.HOME ? 'Home' : 
                   addressType === AddressType.WORK ? 'Office' :
                   addressType === AddressType.BILLING ? 'Billing' : 'Shipping',
            street1: `${randomInt(100, 9999)} ${randomChoice(['Main', 'Oak', 'Pine', 'Elm', 'Cedar'])} ${randomChoice(['St', 'Ave', 'Blvd', 'Dr', 'Ln'])}`,
            street2: randomBoolean(0.3) ? `Apt ${randomInt(1, 999)}` : null,
            city: randomChoice(cities),
            state: randomChoice(states),
            postalCode: generateNumeric(5),
            country: country,
            isDefault: j === 0,
            isVerified: randomBoolean(0.7),
            latitude: randomBoolean(0.4) ? randomFloat(-90, 90) : null,
            longitude: randomBoolean(0.4) ? randomFloat(-180, 180) : null,
          },
        });
      }

      // Generate 1-4 payment methods per customer (only for active customers mostly)
      if (status === CustomerStatus.ACTIVE || randomBoolean(0.3)) {
        const numPaymentMethods = randomInt(1, 4);
        
        for (let k = 0; k < numPaymentMethods; k++) {
          const provider = randomChoice([PaymentProvider.ELAVON, PaymentProvider.TSEP, PaymentProvider.NEARPAY, PaymentProvider.STRIPE]);
          const tokenType = randomChoice([PaymentTokenType.CARD, PaymentTokenType.BANK_ACCOUNT, PaymentTokenType.DIGITAL_WALLET]);
          const cardBrand = tokenType === PaymentTokenType.CARD ? randomChoice(cardBrands) : null;
          
          // Generate token data
          const rawToken = generateAlphanumeric(32);
          const tokenHash = generateTokenHash(rawToken);
          
          let maskedInfo = '';
          let expiresAt = null;
          
          if (tokenType === PaymentTokenType.CARD && cardBrand) {
            const cardNumber = generateCardNumber(cardBrand);
            maskedInfo = maskCardNumber(cardNumber);
            const futureDate = new Date();
            futureDate.setFullYear(futureDate.getFullYear() + randomInt(1, 4));
            expiresAt = futureDate;
          } else if (tokenType === PaymentTokenType.BANK_ACCOUNT) {
            maskedInfo = `****${generateNumeric(4)}`;
          } else {
            maskedInfo = `${cardBrand || 'Digital'} Wallet`;
          }

          // Provider-specific data
          let providerData = {};
          
          if (provider === PaymentProvider.ELAVON) {
            providerData = {
              elavonToken: `ssl_${generateAlphanumeric(16)}`,
              elavonTransactionId: generateNumeric(10),
              elavonReferenceId: generateAlphanumeric(12),
              elavonCardType: cardBrand === 'Visa' ? 'VISA' : 
                             cardBrand === 'Mastercard' ? 'MAST' :
                             cardBrand === 'American Express' ? 'AMEX' : 'DISC',
              elavonApprovalCode: generateAlphanumeric(6),
            };
          } else if (provider === PaymentProvider.TSEP) {
            providerData = {
              tsepToken: `tsep_${generateAlphanumeric(20)}`,
              tsepTransactionId: generateNumeric(12),
              tsepTransactionKey: generateAlphanumeric(16),
              tsepDeviceId: generateAlphanumeric(8),
              tsepCardType: cardBrand === 'Visa' ? 'V' : 
                           cardBrand === 'Mastercard' ? 'M' :
                           cardBrand === 'American Express' ? 'A' : 'D',
              tsepResponseCode: '00',
            };
          }

          const transactionAmount = randomFloat(10, 5000);
          const financeId = randomBoolean(0.6) ? generateUUID() : null;
          const accountId = randomBoolean(0.6) ? generateUUID() : null;

          await prisma.paymentToken.create({
            data: {
              customerId: customer.id,
              tokenHash,
              externalTokenId: `ext_${generateAlphanumeric(16)}`,
              paymentProvider: provider,
              tokenType,
              status: randomBoolean(0.85) ? PaymentTokenStatus.ACTIVE : PaymentTokenStatus.EXPIRED,
              maskedInfo,
              paymentBrand: cardBrand,
              expiresAt,
              transactionAmount,
              currency: 'USD',
              customerName: `${firstName} ${lastName}`,
              customerEmail: email,
              financeId,
              accountId,
              createdByIp: `${randomInt(1, 255)}.${randomInt(1, 255)}.${randomInt(1, 255)}.${randomInt(1, 255)}`,
              lastUsedAt: randomBoolean(0.7) ? new Date(Date.now() - randomInt(0, 30 * 24 * 60 * 60 * 1000)) : null,
              usageCount: randomInt(0, 50),
              rawWebhookData: {
                provider: provider,
                timestamp: new Date().toISOString(),
                amount: transactionAmount,
                currency: 'USD',
                status: 'success'
              },
              providerMetadata: {
                provider: provider,
                ...providerData
              },
              ...providerData
            }
          });
        }
      }

      // Progress indicator
      if ((i + 1) % 10 === 0) {
        console.log(`✅ Created ${i + 1}/${customersToCreate} customers...`);
      }
    }
    
    // Generate summary statistics
    console.log('\n🎉 Database seeding completed successfully!');
    await generateSummaryReport();
    
  } catch (error) {
    console.error('❌ Error during seeding:', error);

    // Provide helpful error messages for common issues
    if (error.message.includes('P2002')) {
      console.error('💡 Tip: This appears to be a unique constraint violation. The seeding script may have been run multiple times.');
    } else if (error.message.includes('P2021')) {
      console.error('💡 Tip: Table does not exist. Please run database migrations first: npx prisma migrate deploy');
    } else if (error.message.includes('P1001')) {
      console.error('💡 Tip: Cannot reach database server. Please check your DATABASE_URL and ensure the database is running.');
    } else if (error.message.includes('P1003')) {
      console.error('💡 Tip: Database does not exist. Please create the database or check your DATABASE_URL.');
    }

    throw error;
  }
}

async function generateSummaryReport() {
  const totalCustomers = await prisma.customer.count();
  const totalAddresses = await prisma.address.count();
  const totalPaymentTokens = await prisma.paymentToken.count();
  
  const customersByStatus = await prisma.customer.groupBy({
    by: ['status'],
    _count: { status: true },
  });
  
  const customersByType = await prisma.customer.groupBy({
    by: ['type'],
    _count: { type: true },
  });
  
  const paymentTokensByProvider = await prisma.paymentToken.groupBy({
    by: ['paymentProvider'],
    _count: { paymentProvider: true },
  });

  console.log('\n📊 PRODUCTION SEEDING SUMMARY:');
  console.log('=' .repeat(50));
  console.log(`👥 Total Customers: ${totalCustomers}`);
  console.log(`📍 Total Addresses: ${totalAddresses}`);
  console.log(`💳 Total Payment Tokens: ${totalPaymentTokens}`);
  console.log(`📈 Average Addresses per Customer: ${(totalAddresses / totalCustomers).toFixed(2)}`);
  console.log(`💰 Average Payment Methods per Customer: ${(totalPaymentTokens / totalCustomers).toFixed(2)}`);
  
  console.log('\n📈 Customer Status Distribution:');
  customersByStatus.forEach(item => {
    const percentage = ((item._count.status / totalCustomers) * 100).toFixed(1);
    console.log(`   ${item.status}: ${item._count.status} (${percentage}%)`);
  });
  
  console.log('\n🏢 Customer Type Distribution:');
  customersByType.forEach(item => {
    const percentage = ((item._count.type / totalCustomers) * 100).toFixed(1);
    console.log(`   ${item.type}: ${item._count.type} (${percentage}%)`);
  });
  
  console.log('\n💰 Payment Provider Distribution:');
  paymentTokensByProvider.forEach(item => {
    const percentage = ((item._count.paymentProvider / totalPaymentTokens) * 100).toFixed(1);
    console.log(`   ${item.paymentProvider}: ${item._count.paymentProvider} (${percentage}%)`);
  });
  
  console.log('\n✨ Production-ready test data generated successfully!');
  console.log('🚀 Ready for development and testing!');
}

// Execute the seeding script with robust error handling
main()
  .catch((e) => {
    console.error('❌ Fatal error during seeding:', e);
    console.error('\n🔧 Troubleshooting Steps:');
    console.error('1. Verify DATABASE_URL is correctly set in your .env file');
    console.error('2. Ensure the database server is running and accessible');
    console.error('3. Run database migrations: npx prisma migrate deploy');
    console.error('4. Check database permissions and credentials');
    console.error('5. Verify the database name exists in your PostgreSQL instance');
    process.exit(1);
  })
  .finally(async () => {
    try {
      await prisma.$disconnect();
      console.log('\n🔌 Database connection closed gracefully');
    } catch (error) {
      console.error('⚠️ Warning: Error closing database connection:', error.message);
    }
  });
