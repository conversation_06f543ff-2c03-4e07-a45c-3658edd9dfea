#!/bin/bash

# Staging Authentication and CORS Testing Script
# This script tests the fixes for authentication and CORS issues in staging

STAGING_URL="https://ng-support-dev.dev1.ngnair.com"
STAGING_ORIGIN="https://ng-support-dev.dev1.ngnair.com"

echo "🧪 Testing Staging Authentication and CORS Fixes"
echo "================================================"
echo "Staging URL: $STAGING_URL"
echo "Testing Origin: $STAGING_ORIGIN"
echo ""

# Test 1: Health Check
echo "1️⃣ Testing Health Endpoint..."
echo "curl -s $STAGING_URL/api/health"
HEALTH_RESPONSE=$(curl -s "$STAGING_URL/api/health")
if [[ $? -eq 0 ]]; then
    echo "✅ Health endpoint accessible"
    echo "Response: $HEALTH_RESPONSE"
else
    echo "❌ Health endpoint failed"
fi
echo ""

# Test 2: CORS Preflight
echo "2️⃣ Testing CORS Preflight..."
echo "curl -H \"Origin: $STAGING_ORIGIN\" -H \"Access-Control-Request-Method: GET\" -X OPTIONS $STAGING_URL/auth/me"
CORS_RESPONSE=$(curl -s -I \
    -H "Origin: $STAGING_ORIGIN" \
    -H "Access-Control-Request-Method: GET" \
    -H "Access-Control-Request-Headers: Content-Type" \
    -X OPTIONS \
    "$STAGING_URL/auth/me")

if echo "$CORS_RESPONSE" | grep -q "Access-Control-Allow-Origin"; then
    echo "✅ CORS preflight successful"
    echo "CORS Headers:"
    echo "$CORS_RESPONSE" | grep -i "access-control"
else
    echo "❌ CORS preflight failed"
    echo "Response headers:"
    echo "$CORS_RESPONSE"
fi
echo ""

# Test 3: Auth endpoint without cookies (should return 401)
echo "3️⃣ Testing Auth Endpoint Without Cookies..."
echo "curl -s -w \"%{http_code}\" $STAGING_URL/auth/me"
AUTH_STATUS=$(curl -s -w "%{http_code}" -o /dev/null "$STAGING_URL/auth/me")
if [[ "$AUTH_STATUS" == "401" ]]; then
    echo "✅ Auth endpoint correctly returns 401 without cookies"
else
    echo "❌ Auth endpoint returned unexpected status: $AUTH_STATUS"
fi
echo ""

# Test 4: CORS with actual request
echo "4️⃣ Testing CORS with Actual Request..."
echo "curl -H \"Origin: $STAGING_ORIGIN\" $STAGING_URL/auth/me"
CORS_ACTUAL=$(curl -s -I \
    -H "Origin: $STAGING_ORIGIN" \
    "$STAGING_URL/auth/me")

if echo "$CORS_ACTUAL" | grep -q "Access-Control-Allow-Origin"; then
    echo "✅ CORS working for actual requests"
    echo "CORS Headers:"
    echo "$CORS_ACTUAL" | grep -i "access-control"
else
    echo "❌ CORS not working for actual requests"
    echo "Response headers:"
    echo "$CORS_ACTUAL"
fi
echo ""

# Test 5: GraphQL endpoint
echo "5️⃣ Testing GraphQL Endpoint..."
echo "curl -s -w \"%{http_code}\" $STAGING_URL/graphql"
GRAPHQL_STATUS=$(curl -s -w "%{http_code}" -o /dev/null "$STAGING_URL/graphql")
if [[ "$GRAPHQL_STATUS" == "400" ]] || [[ "$GRAPHQL_STATUS" == "200" ]]; then
    echo "✅ GraphQL endpoint accessible (status: $GRAPHQL_STATUS)"
else
    echo "❌ GraphQL endpoint failed (status: $GRAPHQL_STATUS)"
fi
echo ""

# Test 6: Check if staging environment is detected
echo "6️⃣ Testing Environment Detection..."
echo "This requires checking the application logs for:"
echo "- '🎭 Environment: STAGING'"
echo "- CORS debug messages showing allowed origins"
echo "- Enhanced cookie debugging messages"
echo ""
echo "To check logs, run:"
echo "docker-compose -f docker-compose.staging.yml logs -f support-backend-api | grep -E '(Environment|CORS|AUTH SERVICE)'"
echo ""

echo "🏁 Testing Complete!"
echo ""
echo "📋 Summary of Expected Results:"
echo "✅ Health endpoint should be accessible"
echo "✅ CORS preflight should include Access-Control-Allow-Origin header"
echo "✅ Auth endpoints should return 401 without cookies"
echo "✅ CORS should work for actual requests from staging origin"
echo "✅ GraphQL endpoint should be accessible"
echo ""
echo "🔧 If any tests fail, check:"
echo "1. Application logs for detailed error messages"
echo "2. Docker container is running with NODE_ENV=staging"
echo "3. CORS configuration includes the staging domain"
echo "4. Cookie settings are appropriate for HTTPS"
echo ""
echo "📖 For detailed troubleshooting, see: STAGING_DEPLOYMENT_GUIDE.md"
