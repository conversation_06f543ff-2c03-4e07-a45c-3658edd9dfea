import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '../../../config/config.service';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { AxiosResponse } from 'axios';
import { 
  UserScopes, 
  PartnerInfo, 
  SupportScope, 
  TenantType,
  ScopeContext 
} from './types/scope.types';

@Injectable()
export class ScopeResolutionService {
  private readonly logger = new Logger(ScopeResolutionService.name);
  private readonly partnerApiBaseUrl: string;

  // Cache for partner info and user scopes
  private partnerInfoCache = new Map<string, { data: PartnerInfo; timestamp: number }>();
  private userScopesCache = new Map<string, { data: UserScopes; timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor(
    private configService: ConfigService,
    private httpService: HttpService
  ) {
    this.partnerApiBaseUrl = 'https://ng-partner-dev.dev1.ngnair.com/api/v1';
    this.logger.log(`🔧 [SCOPE RESOLUTION] Initialized with partner API: ${this.partnerApiBaseUrl}`);
  }

  /**
   * Resolve user scopes from external APIs
   * @param userId - The user ID (sub from JWT)
   * @param accessToken - The access token for authentication
   * @returns Promise<ScopeContext> - User scope context
   */
  async resolveUserScopes(userId: string, accessToken?: string): Promise<ScopeContext> {
    try {
      this.logger.log(`🔍 [SCOPE RESOLUTION] Resolving scopes for user: ${userId}`);

      // Step 1: Get partner information
      const partnerInfo = await this.getPartnerInfo(accessToken);
      this.logger.log(`🏢 [SCOPE RESOLUTION] Partner info: ${JSON.stringify(partnerInfo)}`);

      // Step 2: Get user scopes for partner context
      const userScopes = await this.getUserScopes(partnerInfo.public_uid, userId, accessToken, TenantType.PARTNER);
      
      // Step 3: Determine the highest support scope
      const supportScope = this.determineSupportScope(userScopes.scopes);
      
      const scopeContext: ScopeContext = {
        scope: supportScope,
        tenantType: userScopes.tenantType,
        tenantId: userScopes.tenantId,
        userId: userId,
        partnerInfo: partnerInfo
      };

      this.logger.log(`✅ [SCOPE RESOLUTION] Resolved scope context: ${JSON.stringify(scopeContext)}`);
      return scopeContext;

    } catch (error) {
      this.logger.error(`❌ [SCOPE RESOLUTION] Failed to resolve scopes for user ${userId}:`, error.message);
      
      // Return default user scope as fallback
      return {
        scope: SupportScope.PARTNER_SUPPORT_USER,
        tenantType: TenantType.PARTNER,
        tenantId: undefined,
        userId: userId
      };
    }
  }

  /**
   * Get partner information from external API
   */
  private async getPartnerInfo(accessToken?: string): Promise<PartnerInfo> {
    const cacheKey = 'partner_info';
    const cached = this.partnerInfoCache.get(cacheKey);
    
    if (cached && (Date.now() - cached.timestamp) < this.CACHE_TTL) {
      this.logger.log(`🎯 [SCOPE RESOLUTION] Using cached partner info`);
      return cached.data;
    }

    try {
      const headers: any = {
        'Content-Type': 'application/json',
        'User-Agent': 'Support-Backend/1.0',
      };

      if (accessToken) {
        headers['Cookie'] = `access_token=${accessToken}`;
      }

      const response: AxiosResponse<PartnerInfo[]> = await firstValueFrom(
        this.httpService.get(`${this.partnerApiBaseUrl}/partners`, {
          headers,
          timeout: 10000,
        })
      );

      if (response.status === 200 && response.data && response.data.length > 0) {
        const partnerInfo = response.data[0]; // Assuming first partner is the current one
        
        // Cache the result
        this.partnerInfoCache.set(cacheKey, {
          data: partnerInfo,
          timestamp: Date.now()
        });

        return partnerInfo;
      } else {
        throw new HttpException('No partner information found', HttpStatus.NOT_FOUND);
      }
    } catch (error) {
      this.logger.error(`❌ [SCOPE RESOLUTION] Failed to fetch partner info:`, error.message);
      throw error;
    }
  }

  /**
   * Get user scopes from external API
   */
  private async getUserScopes(
    partnerId: string, 
    userId: string, 
    accessToken?: string,
    tenantType: TenantType = TenantType.PARTNER
  ): Promise<UserScopes> {
    const cacheKey = `${partnerId}_${userId}_${tenantType}`;
    const cached = this.userScopesCache.get(cacheKey);
    
    if (cached && (Date.now() - cached.timestamp) < this.CACHE_TTL) {
      this.logger.log(`🎯 [SCOPE RESOLUTION] Using cached user scopes for ${userId}`);
      return cached.data;
    }

    try {
      const headers: any = {
        'Content-Type': 'application/json',
        'User-Agent': 'Support-Backend/1.0',
        'X-Tenant-Type': tenantType,
        'X-Tenant-Id': partnerId,
      };

      if (accessToken) {
        headers['Cookie'] = `access_token=${accessToken}`;
      }

      const response: AxiosResponse<any> = await firstValueFrom(
        this.httpService.get(
          `${this.partnerApiBaseUrl}/partners/${partnerId}/partner_users/${userId}/scopes`,
          {
            headers,
            timeout: 10000,
          }
        )
      );

      if (response.status === 200 && response.data) {
        // Handle both array and object response formats
        let scopesArray: string[];
        if (Array.isArray(response.data)) {
          scopesArray = response.data;
        } else if (response.data.scopes && Array.isArray(response.data.scopes)) {
          scopesArray = response.data.scopes;
        } else {
          throw new Error('Invalid scopes response format');
        }

        const userScopes: UserScopes = {
          scopes: scopesArray,
          tenantType: tenantType,
          tenantId: partnerId,
          userId: userId
        };
        
        // Cache the result
        this.userScopesCache.set(cacheKey, {
          data: userScopes,
          timestamp: Date.now()
        });

        return userScopes;
      } else {
        throw new HttpException('No scopes found for user', HttpStatus.NOT_FOUND);
      }
    } catch (error) {
      this.logger.error(`❌ [SCOPE RESOLUTION] Failed to fetch user scopes:`, error.message);
      throw error;
    }
  }

  /**
   * Determine the highest support scope from user scopes
   */
  private determineSupportScope(scopes: string[]): SupportScope {
    this.logger.log(`🔍 [SCOPE RESOLUTION] Determining support scope from scopes: ${JSON.stringify(scopes)}`);

    // Check for global admin first
    if (scopes.includes(SupportScope.GLOBAL_SUPPORT_ADMIN)) {
      this.logger.log(`✅ [SCOPE RESOLUTION] Found global admin scope: ${SupportScope.GLOBAL_SUPPORT_ADMIN}`);
      return SupportScope.GLOBAL_SUPPORT_ADMIN;
    }

    // Check for partner admin
    if (scopes.includes(SupportScope.PARTNER_SUPPORT_ADMIN)) {
      this.logger.log(`✅ [SCOPE RESOLUTION] Found partner admin scope: ${SupportScope.PARTNER_SUPPORT_ADMIN}`);
      return SupportScope.PARTNER_SUPPORT_ADMIN;
    }

    // Check for account admin
    if (scopes.includes(SupportScope.ACCOUNT_SUPPORT_ADMIN)) {
      this.logger.log(`✅ [SCOPE RESOLUTION] Found account admin scope: ${SupportScope.ACCOUNT_SUPPORT_ADMIN}`);
      return SupportScope.ACCOUNT_SUPPORT_ADMIN;
    }

    // Check for partner user
    if (scopes.includes(SupportScope.PARTNER_SUPPORT_USER)) {
      this.logger.log(`✅ [SCOPE RESOLUTION] Found partner user scope: ${SupportScope.PARTNER_SUPPORT_USER}`);
      return SupportScope.PARTNER_SUPPORT_USER;
    }

    // Check for account user
    if (scopes.includes(SupportScope.ACCOUNT_SUPPORT_USER)) {
      this.logger.log(`✅ [SCOPE RESOLUTION] Found account user scope: ${SupportScope.ACCOUNT_SUPPORT_USER}`);
      return SupportScope.ACCOUNT_SUPPORT_USER;
    }

    // Default fallback
    this.logger.warn(`⚠️ [SCOPE RESOLUTION] No support scopes found, defaulting to: ${SupportScope.PARTNER_SUPPORT_USER}`);
    return SupportScope.PARTNER_SUPPORT_USER;
  }

  /**
   * Extract access token from request cookies
   */
  extractAccessToken(cookies: any): string | undefined {
    if (cookies && cookies.access_token) {
      return cookies.access_token;
    }
    return undefined;
  }

  /**
   * Clear cache for a specific user
   */
  clearUserCache(userId: string): void {
    // Clear all cache entries for this user
    for (const [key] of this.userScopesCache) {
      if (key.includes(userId)) {
        this.userScopesCache.delete(key);
      }
    }
    this.logger.log(`🗑️ [SCOPE RESOLUTION] Cleared cache for user ${userId}`);
  }

  /**
   * Clear all caches
   */
  clearAllCache(): void {
    this.partnerInfoCache.clear();
    this.userScopesCache.clear();
    this.logger.log(`🗑️ [SCOPE RESOLUTION] Cleared all scope resolution cache`);
  }
}
