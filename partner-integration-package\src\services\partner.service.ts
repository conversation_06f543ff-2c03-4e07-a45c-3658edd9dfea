import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { AxiosResponse } from 'axios';
import {
  PartnerConfig,
  PartnerInfo,
  UserScopes,
  PartnerApiResponse,
  GetUserScopesParams,
  GetPartnerInfoParams,
  PartnerListResponse,
  TenantType,
  ScopeContext,
} from '../types/partner.types';

@Injectable()
export class PartnerService {
  private readonly logger = new Logger(PartnerService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly config: PartnerConfig
  ) {
    this.logger.log(`🔧 [PARTNER SERVICE] Initialized with API base URL: ${this.config.partnerApiBaseUrl}`);
  }

  /**
   * Get all partners
   */
  async getPartners(accessToken: string): Promise<PartnerApiResponse<PartnerInfo[]>> {
    try {
      this.logDebug('Getting all partners...');

      const headers = {
        ...this.config.defaultHeaders,
        'Cookie': `access_token=${accessToken}`,
      };

      const response: AxiosResponse<PartnerInfo[]> = await firstValueFrom(
        this.httpService.get(`${this.config.partnerApiBaseUrl}/partners`, {
          headers,
          timeout: this.config.requestTimeout,
        })
      );

      this.logDebug(`Partners API response status: ${response.status}`);

      return {
        success: true,
        data: response.data,
        message: 'Partners retrieved successfully'
      };

    } catch (error) {
      this.logger.error(`❌ [PARTNER SERVICE] Error getting partners: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get partner information by partner ID
   */
  async getPartnerInfo(params: GetPartnerInfoParams, accessToken: string): Promise<PartnerApiResponse<PartnerInfo>> {
    try {
      this.logDebug(`Getting partner info for: ${params.partnerId}`);

      const headers = {
        ...this.config.defaultHeaders,
        'Cookie': `access_token=${accessToken}`,
      };

      const response: AxiosResponse<PartnerInfo> = await firstValueFrom(
        this.httpService.get(`${this.config.partnerApiBaseUrl}/partners/${params.partnerId}`, {
          headers,
          timeout: this.config.requestTimeout,
        })
      );

      this.logDebug(`Partner info API response status: ${response.status}`);

      return {
        success: true,
        data: response.data,
        message: 'Partner info retrieved successfully'
      };

    } catch (error) {
      this.logger.error(`❌ [PARTNER SERVICE] Error getting partner info: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get user scopes for a specific partner and user
   */
  async getUserScopes(params: GetUserScopesParams, accessToken: string): Promise<PartnerApiResponse<UserScopes>> {
    try {
      this.logDebug(`Getting user scopes for user ${params.userId} in partner ${params.partnerId}`);

      const headers = {
        ...this.config.defaultHeaders,
        'X-Tenant-Type': params.tenantType || 'partner',
        'X-Tenant-Id': params.partnerId,
        'Cookie': `access_token=${accessToken}`,
      };

      const url = `${this.config.partnerApiBaseUrl}/partners/${params.partnerId}/partner_users/${params.userId}/scopes`;
      
      const response: AxiosResponse<any> = await firstValueFrom(
        this.httpService.get(url, {
          headers,
          timeout: this.config.requestTimeout,
        })
      );

      this.logDebug(`User scopes API response status: ${response.status}`);

      // Handle both array and object response formats
      let scopesArray: string[];
      if (Array.isArray(response.data)) {
        scopesArray = response.data;
      } else if (response.data.scopes && Array.isArray(response.data.scopes)) {
        scopesArray = response.data.scopes;
      } else {
        throw new Error('Invalid scopes response format');
      }

      const userScopes: UserScopes = {
        user_id: params.userId,
        partner_id: params.partnerId,
        scopes: scopesArray
      };

      return {
        success: true,
        data: userScopes,
        message: 'User scopes retrieved successfully'
      };

    } catch (error) {
      this.logger.error(`❌ [PARTNER SERVICE] Error getting user scopes: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Resolve complete scope context for a user
   */
  async resolveScopeContext(params: GetUserScopesParams, accessToken: string): Promise<PartnerApiResponse<ScopeContext>> {
    try {
      this.logDebug(`Resolving scope context for user ${params.userId}`);

      // Get user scopes
      const scopesResult = await this.getUserScopes(params, accessToken);
      if (!scopesResult.success) {
        return scopesResult;
      }

      // Get partner info
      const partnerResult = await this.getPartnerInfo({ partnerId: params.partnerId }, accessToken);
      if (!partnerResult.success) {
        return partnerResult;
      }

      const scopeContext: ScopeContext = {
        userId: params.userId,
        partnerId: params.partnerId,
        tenantType: (params.tenantType as TenantType) || TenantType.PARTNER,
        scopes: scopesResult.data!.scopes,
        partnerInfo: partnerResult.data
      };

      return {
        success: true,
        data: scopeContext,
        message: 'Scope context resolved successfully'
      };

    } catch (error) {
      this.logger.error(`❌ [PARTNER SERVICE] Error resolving scope context: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Helper method for debug logging
   */
  private logDebug(message: string): void {
    if (this.config.enableDebugLogging) {
      this.logger.debug(`🔍 [PARTNER SERVICE] ${message}`);
    }
  }
}
