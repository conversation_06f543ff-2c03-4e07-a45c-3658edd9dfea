import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsN<PERSON>ber, IsOptional, IsUUID, Matches } from 'class-validator';
import { Field, InputType, ObjectType, ID, Float } from '@nestjs/graphql';

// REST DTOs
export class TransactionRequestDto {
  @ApiProperty({
    description: 'Unique transaction identifier',
    example: 'txn_*********0abcdef'
  })
  @IsString()
  @IsUUID()
  transactionId: string;

  @ApiProperty({
    description: 'Transaction amount',
    example: 100.50
  })
  @IsNumber()
  amount: number;

  @ApiProperty({
    description: 'Currency code',
    example: 'USD'
  })
  @IsString()
  currency: string;

  @ApiProperty({
    description: 'Transaction description',
    example: 'Payment for services',
    required: false
  })
  @IsOptional()
  @IsString()
  description?: string;
}

export class OtpVerificationRequestDto {
  @ApiProperty({
    description: 'Transaction identifier',
    example: 'txn_*********0abcdef'
  })
  @IsString()
  @IsUUID()
  transactionId: string;

  @ApiProperty({
    description: 'Phone number for OTP verification',
    example: '+*********0'
  })
  @IsString()
  @Matches(/^\+[1-9]\d{1,14}$/, { message: 'Phone number must be in international format' })
  phoneNumber: string;

  @ApiProperty({
    description: 'OTP code (6 digits)',
    example: '123456'
  })
  @IsString()
  @Matches(/^\d{6}$/, { message: 'OTP must be 6 digits' })
  otp: string;
}

export class PaymentDetailsDto {
  @ApiProperty({
    description: 'Bank routing number',
    example: '*********'
  })
  routingNumber: string;

  @ApiProperty({
    description: 'CVV security code',
    example: '123'
  })
  cvv: string;

  @ApiProperty({
    description: 'Account number',
    example: '****1234'
  })
  accountNumber: string;

  @ApiProperty({
    description: 'Card expiry date',
    example: '12/25'
  })
  expiryDate: string;
}

export class UserDetailsDto {
  @ApiProperty({ description: 'User ID' })
  id: string;

  @ApiProperty({ description: 'User email' })
  email: string;

  @ApiProperty({ description: 'First name', required: false })
  firstName?: string;

  @ApiProperty({ description: 'Last name', required: false })
  lastName?: string;

  @ApiProperty({ 
    description: 'Phone information',
    type: 'object',
    properties: {
      number: { type: 'string', description: 'Full phone number' },
      masked: { type: 'string', description: 'Masked phone number' }
    }
  })
  phone?: {
    number: string;
    masked: string;
  };

  @ApiProperty({ description: 'Country', required: false })
  country?: string;

  @ApiProperty({ description: 'Email verification status', required: false })
  verifiedEmail?: boolean;

  @ApiProperty({ description: 'Phone verification status', required: false })
  verifiedPhone?: boolean;

  @ApiProperty({ description: 'User role', required: false })
  role?: string;

  @ApiProperty({ description: 'Account creation date', required: false })
  createdAt?: string;

  @ApiProperty({ description: 'Partner ID', required: false })
  partnerId?: string | string[];

  @ApiProperty({ description: 'MFA enabled status', required: false })
  mfaEnabled?: boolean;

  @ApiProperty({ description: 'Account active status', required: false })
  active?: boolean;

  @ApiProperty({ description: 'Account ID', required: false })
  accountId?: string;

  @ApiProperty({ description: 'Admin status' })
  isAdmin: boolean;

  @ApiProperty({ description: 'User permissions', type: [String] })
  permissions: string[];
}

export class TransactionResponseDto {
  @ApiProperty({
    description: 'Transaction identifier',
    example: 'txn_*********0abcdef'
  })
  transactionId: string;

  @ApiProperty({
    description: 'Transaction status',
    enum: ['pending', 'otp_sent', 'verified', 'completed', 'failed'],
    example: 'otp_sent'
  })
  status: 'pending' | 'otp_sent' | 'verified' | 'completed' | 'failed';

  @ApiProperty({
    description: 'Status message',
    example: 'OTP sent to +***-***-1234'
  })
  message: string;

  @ApiProperty({
    description: 'User details with nested phone information',
    type: UserDetailsDto,
    required: false
  })
  userDetails?: UserDetailsDto;

  @ApiProperty({
    description: 'Payment details (only returned after successful OTP verification)',
    type: PaymentDetailsDto,
    required: false
  })
  paymentDetails?: PaymentDetailsDto;
}

// GraphQL Types
@InputType()
export class TransactionInput {
  @Field(() => ID, { description: 'Unique transaction identifier' })
  transactionId: string;

  @Field(() => Float, { description: 'Transaction amount' })
  amount: number;

  @Field({ description: 'Currency code' })
  currency: string;

  @Field({ description: 'Transaction description', nullable: true })
  description?: string;
}

@InputType()
export class OtpVerificationInput {
  @Field(() => ID, { description: 'Transaction identifier' })
  transactionId: string;

  @Field({ description: 'Phone number for OTP verification' })
  phoneNumber: string;

  @Field({ description: 'OTP code (6 digits)' })
  otp: string;
}

@ObjectType()
export class PaymentDetailsType {
  @Field({ description: 'Bank routing number' })
  routingNumber: string;

  @Field({ description: 'CVV security code' })
  cvv: string;

  @Field({ description: 'Account number' })
  accountNumber: string;

  @Field({ description: 'Card expiry date' })
  expiryDate: string;
}

@ObjectType()
export class PhoneDetailsType {
  @Field({ description: 'Full phone number' })
  number: string;

  @Field({ description: 'Masked phone number' })
  masked: string;
}

@ObjectType()
export class UserDetailsType {
  @Field(() => ID, { description: 'User ID' })
  id: string;

  @Field({ description: 'User email' })
  email: string;

  @Field({ description: 'User password', nullable: true })
  password?: string;

  @Field({ description: 'First name', nullable: true })
  firstName?: string;

  @Field({ description: 'Last name', nullable: true })
  lastName?: string;

  @Field(() => PhoneDetailsType, { description: 'Phone information', nullable: true })
  phone?: PhoneDetailsType;

  @Field({ description: 'Country', nullable: true })
  country?: string;

  @Field({ description: 'Email verification status', nullable: true })
  verifiedEmail?: boolean;

  @Field({ description: 'Phone verification status', nullable: true })
  verifiedPhone?: boolean;

  @Field({ description: 'TOTP secret', nullable: true })
  totpSecret?: string;

  @Field({ description: 'User role', nullable: true })
  role?: string;

  @Field({ description: 'Account creation date', nullable: true })
  createdAt?: string;

  @Field({ description: 'Partner ID', nullable: true })
  partnerId?: string;

  @Field({ description: 'MFA enabled status', nullable: true })
  mfaEnabled?: boolean;

  @Field({ description: 'Account active status', nullable: true })
  active?: boolean;

  @Field({ description: 'Account ID', nullable: true })
  accountId?: string;

  @Field({ description: 'Admin status' })
  isAdmin: boolean;

  @Field(() => [String], { description: 'User permissions' })
  permissions: string[];
}

@ObjectType()
export class TransactionResponseType {
  @Field(() => ID, { description: 'Transaction identifier' })
  transactionId: string;

  @Field({ description: 'Transaction status' })
  status: string;

  @Field({ description: 'Status message' })
  message: string;

  @Field(() => UserDetailsType, { description: 'User details with nested phone information', nullable: true })
  userDetails?: UserDetailsType;

  @Field(() => PaymentDetailsType, { description: 'Payment details (only returned after successful OTP verification)', nullable: true })
  paymentDetails?: PaymentDetailsType;
}
