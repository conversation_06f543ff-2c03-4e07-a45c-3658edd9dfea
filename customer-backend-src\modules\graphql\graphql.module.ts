import { Module } from '@nestjs/common';
import { GraphQLModule } from '@nestjs/graphql';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { join } from 'path';
import { FastifyRequest, FastifyReply } from 'fastify';
import { GraphQLError } from 'graphql';
import { ConfigService } from '../../config/config.service';
import { ConfigModule } from '../../config/config.module';
import { PrismaModule } from '../../prisma/prisma.module';
import { AuthSharedModule } from '../auth-shared/auth-shared.module';
import { CustomersQueryModule } from '../customers-query/customers-query.module';
import { CustomersManagementModule } from '../customers-management/customers-management.module';
import { CustomersVerificationModule } from '../customers-verification/customers-verification.module';
import { CustomersAdminModule } from '../customers-admin/customers-admin.module';
import { CustomerResolver } from './resolvers/customer.resolver';
import { AuthResolver } from './resolvers/auth.resolver';
import { GraphQLAuthGuard } from './guards/graphql-auth.guard';

@Module({
  imports: [
    GraphQLModule.forRootAsync<ApolloDriverConfig>({
      driver: ApolloDriver,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        autoSchemaFile: join(process.cwd(), 'src/schema.gql'),
        sortSchema: true,
        playground: configService.get('NODE_ENV', 'development') === 'development' ? {
          settings: {
            'request.credentials': 'include',
          },
        } : false,
        introspection: true,
        context: ({ req, reply }: { req: FastifyRequest; reply: FastifyReply }) => ({ req, reply }),
        formatError: (error: GraphQLError) => {
          // Log the error for debugging
          console.error('GraphQL Error:', error);
          return {
            message: error.message,
            code: error.extensions?.code,
            path: error.path,
          };
        },
        cors: {
          origin: configService.getAllowedOrigins(),
          credentials: true,
        },
      }),
    }),
    ConfigModule,
    PrismaModule,
    AuthSharedModule,
    CustomersQueryModule,
    CustomersManagementModule,
    CustomersVerificationModule,
    CustomersAdminModule,
  ],
  providers: [CustomerResolver, AuthResolver, GraphQLAuthGuard],
  exports: [GraphQLAuthGuard],
})
export class GraphQLConfigModule {}
