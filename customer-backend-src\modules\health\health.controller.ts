import { Controller, Get, UseGuards, UnauthorizedException, ForbiddenException } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags, ApiUnauthorizedResponse, ApiForbiddenResponse } from "@nestjs/swagger";
import { AdminGuard, ApiGuard, AuthUser, getCurrentUser } from "../auth-shared";
import { HealthService } from "./health.service";

@ApiTags("health")
@Controller("health")
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @Get("public")
  @ApiOperation({ summary: "Public health check endpoint" })
  @ApiResponse({
    status: 200,
    description: "Service health status - Public access",
    schema: {
      properties: {
        status: { type: "string", example: "ok" },
        timestamp: { type: "string", format: "date-time" },
        access: { type: "string", example: "public" },
        message: { type: "string", example: "Basic health check" }
      }
    }
  })
  healthCheckPublic() {
    return this.healthService.checkHealth("public");
  }

  @Get("user")
  @UseGuards(ApiGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "User health check endpoint - Requires user authentication" })
  @ApiResponse({
    status: 200,
    description: "Service health status - User access",
    schema: {
      properties: {
        status: { type: "string", example: "ok" },
        timestamp: { type: "string", format: "date-time" },
        access: { type: "string", example: "user" },
        message: { type: "string", example: "Authenticated health check" }
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: "User is not authenticated",
    schema: {
      properties: {
        statusCode: { type: "number", example: 401 },
        message: { type: "string", example: "Unauthorized access" }
      }
    }
  })
  healthCheckUser(@getCurrentUser() user: AuthUser) {
    if (!user) {
      throw new UnauthorizedException("Authentication required");
    }
    return this.healthService.checkHealth("user");
  }

  @Get("admin")
  @UseGuards(AdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Admin health check endpoint - Requires admin authentication" })
  @ApiResponse({
    status: 200,
    description: "Service health status - Admin access",
    schema: {
      properties: {
        status: { type: "string", example: "ok" },
        timestamp: { type: "string", format: "date-time" },
        access: { type: "string", example: "admin" },
        message: { type: "string", example: "Full system health check with details" },
        details: {
          type: "object",
          properties: {
            database: { type: "boolean" },
            cache: { type: "boolean" },
            auth: { type: "boolean" }
          }
        }
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: "User is not authenticated",
    schema: {
      properties: {
        statusCode: { type: "number", example: 401 },
        message: { type: "string", example: "Unauthorized access" }
      }
    }
  })
  @ApiForbiddenResponse({
    description: "User is not an admin",
    schema: {
      properties: {
        statusCode: { type: "number", example: 403 },
        message: { type: "string", example: "Access denied: Admin privileges required" }
      }
    }
  })
  healthCheckAdmin(@getCurrentUser() user: AuthUser) {
    if (!user) {
      throw new UnauthorizedException("Authentication required");
    }
    
    if (!user.isAdmin) {
      throw new ForbiddenException("Access denied: Admin privileges required");
    }

    return this.healthService.checkHealth("admin");
  }
}
