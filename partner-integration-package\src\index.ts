/**
 * @ngnair/partner-integration-package
 * 
 * Complete partner integration module package for NestJS applications
 * with partner API calls, scope resolution, and user management
 */

// Main module
export { PartnerModule } from './partner.module';

// Services
export { PartnerService } from './services/partner.service';

// Controllers
export { PartnerController } from './controllers/partner.controller';

// Types
export * from './types/partner.types';

// Re-export commonly used types for convenience
export type {
  PartnerConfig,
  PartnerInfo,
  UserScopes,
  PartnerApiResponse,
  GetUserScopesParams,
  GetPartnerInfoParams,
  PartnerListResponse,
  ScopeContext,
} from './types/partner.types';

export { TenantType, DEFAULT_PARTNER_CONFIG } from './types/partner.types';
