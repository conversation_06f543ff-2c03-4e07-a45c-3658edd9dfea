#!/usr/bin/env node

const http = require('http');
const { URL } = require('url');

// Configuration
const BASE_URL = 'http://ng-support-local.dev.dev1.ngnair.com:3040';
const ACCESS_TOKEN = 'QTJPVkVRSnArRVlSOHZHZC9yUm5EbzIrOXJ4T2JIaUZzWFhCb1kxV2tlNk9odlBEWjRJRml0S0hBOTk2UkNmQ3RXZlNYMHV0NHRiU1hSRWtzbzFRelRWaVJCdHhmNXR3Tm1IR0pSOGdVWTNWNk9ZZ2picXd5dGRKMTRUQUpqOW1hbTlNR3BFaStEN3c0OGxUVUsvcnVJc1hHR0pvUUljdE1KWWIvVURCVEdoZmlIT3JCN050ZUxYVEUrbXlaTEk1NzdOMGlMdDltRWhMNXQxVldRUW9CbjFzREpRYk16ZlF4UDY4NE9xczlIUWF5VTZLUWxyNVJFaHlhNVNNRlhzRzkvdkx6SmJ4QW5PWEk0cUdMcnp1ak5sRGxiUDc4eVpSR204QmlOZkZBNlY1T0h2T05MZjhFcWUxUHpsZ3B0MzBTb2JyOUxaR2RUYmtaeXl4ZE9rTFNpc2VPYW1zT2JrQjVGRkdpUHUreWdBK3Zna3JzWk9NS0FuN1gwSk9GOFhEeDh0NUlvNjF2Y2VZaW52enk0aE5Td1g2MkRmNmtzS2taTmdQeTJTcDh2WWd2cDZzNG9DNitscERDWEwrTnRVdFdvSzZTZGZKbCtBTEFzaTczNmRXMm5kdk55cjgzV3VOcS93M1NrQWtoclNlVTk3d3dOUXMwVGxrOTVXdHhMb1FxbStDcGE0K0xmSnppNXJxV0JOTEg0NjRQdnY0cndPcUlxcFQ3dDBYcWpTU21xQ1lJazJQenNGbVA2OENlQWhtQWN0RjBZMmRsQXRidXN4MVVOdE4rY1pVKzVxZjRINGg5eGZlL3dKVlo0b2E4cVFXd1dVdzZOa3Z4ZHJRZGV1cHFpVXBVWE9IVWJPMEREMlFWN3R0SDFRTkdWbjNva3hhRkhzVXljVGk3VUZRQmoycnBFU3VEdVorVytSMDZzUmNDa0YvOEUza1pOMjJuMXVTeHFsTmZOc2NYL2RQUGNkWVNVeStEaThFdGxhU2N0N2s0bTlERzR5bCttbXBEaUdab1hGYlNTNVZ5dHRYMzNLR3ZydVNlaDhwY1Y4cWYzelMyeTdrNkNaY1k5dzJ3Q3dic044V29abG0vaHVibDBZdjVmS2R4cThhNXBIQzVMVERXSWJQVVpSRWFVUGtpVEJFcjMvZmpSQUNVbmJTQStnWS9lSDNIeUF0SC84Ykp4dS8yS0F3K2ZXQitiVVduUEh1Wk5vdkZDU21jcTNDeGtyZklZek9tUk1XU0ZFdFZZOEdlVm8xRXFUY1VZZjhvVDZYbU5qQ0tFeHM3UHZWSk1HS0tZRG52NXByeWxFVk1vQkZoczZtNWtRenk1MEI1TCtDZHZlM2tkb3JOL2QrdjFOcjA2cWRGcHVQaktwVnRaeGM5Umw3MzltY3BWc3NkdThMN25mTTFhczZaMm5IQWFkYktldW1wY2orL1NZMXRYNENUOTllTWJaeWMxdnBvNi9oZWpCRkNQLytPaXd1OVd1RzFETTU0UT09LS1nOXZQRTFOMUVkUitPS2VMLS0wMTBaMStmZDRMVkJqdlFZcTNoVnZ3PT0%3D';

console.log('🔐 Testing Token Decryption Analysis');
console.log('====================================');
console.log(`Base URL: ${BASE_URL}`);
console.log(`Token Length: ${ACCESS_TOKEN.length}`);
console.log(`Token (first 100 chars): ${ACCESS_TOKEN.substring(0, 100)}...`);
console.log('');

// Helper function to make HTTP requests
function makeRequest(endpoint, method = 'GET', data = null, cookies = '') {
    return new Promise((resolve, reject) => {
        const url = new URL(endpoint, BASE_URL);
        const options = {
            hostname: url.hostname,
            port: url.port,
            path: url.pathname + url.search,
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Node.js-Test-Client/1.0'
            }
        };

        if (cookies) {
            options.headers['Cookie'] = cookies;
        }

        if (data) {
            const jsonData = JSON.stringify(data);
            options.headers['Content-Length'] = Buffer.byteLength(jsonData);
        }

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonBody = JSON.parse(body);
                    resolve({
                        success: res.statusCode >= 200 && res.statusCode < 300,
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        data: jsonBody,
                        headers: res.headers
                    });
                } catch (e) {
                    resolve({
                        success: false,
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        data: body,
                        headers: res.headers,
                        parseError: e.message
                    });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

// Analyze the token format
function analyzeTokenFormat() {
    console.log('🔍 ANALYZING TOKEN FORMAT');
    console.log('─'.repeat(50));
    
    try {
        // Step 1: URL decode
        console.log('Step 1: URL Decoding...');
        const urlDecoded = decodeURIComponent(ACCESS_TOKEN);
        console.log(`✅ URL decoded length: ${urlDecoded.length}`);
        console.log(`First 100 chars: ${urlDecoded.substring(0, 100)}...`);
        
        // Step 2: Base64 decode
        console.log('\nStep 2: Base64 Decoding...');
        try {
            // Handle URL-safe base64 if needed
            const standardBase64 = urlDecoded.replace(/-/g, '+').replace(/_/g, '/');
            // Add padding if needed
            const paddedBase64 = standardBase64 + '='.repeat((4 - standardBase64.length % 4) % 4);
            const base64Decoded = Buffer.from(paddedBase64, 'base64').toString('utf8');
            console.log(`✅ Base64 decoded length: ${base64Decoded.length}`);
            console.log(`First 100 chars: ${base64Decoded.substring(0, 100)}...`);
            
            // Step 3: Check Rails MessageEncryptor format
            console.log('\nStep 3: Checking Rails MessageEncryptor Format...');
            const parts = base64Decoded.split('--');
            console.log(`✅ Split into ${parts.length} parts`);
            
            if (parts.length === 3) {
                console.log('✅ Valid Rails MessageEncryptor format detected');
                console.log(`   Encrypted data length: ${parts[0].length}`);
                console.log(`   IV length: ${parts[1].length}`);
                console.log(`   Auth tag length: ${parts[2].length}`);
                
                // Try to decode the components
                try {
                    const encryptedData = Buffer.from(parts[0], 'base64');
                    const iv = Buffer.from(parts[1], 'base64');
                    const authTag = Buffer.from(parts[2], 'base64');
                    
                    console.log(`   Encrypted data bytes: ${encryptedData.length}`);
                    console.log(`   IV bytes: ${iv.length}`);
                    console.log(`   Auth tag bytes: ${authTag.length}`);
                    
                    // Check if IV and auth tag have expected lengths for AES-256-GCM
                    if (iv.length === 12 && authTag.length === 16) {
                        console.log('✅ IV and auth tag lengths are correct for AES-256-GCM');
                    } else {
                        console.log(`❌ Unexpected lengths - IV: ${iv.length} (expected 12), Auth tag: ${authTag.length} (expected 16)`);
                    }
                    
                } catch (componentError) {
                    console.log(`❌ Failed to decode components: ${componentError.message}`);
                }
                
            } else {
                console.log(`❌ Invalid format - expected 3 parts, got ${parts.length}`);
                console.log('Raw base64 decoded content:');
                console.log(base64Decoded);
            }
            
        } catch (base64Error) {
            console.log(`❌ Base64 decoding failed: ${base64Error.message}`);
        }
        
    } catch (urlError) {
        console.log(`❌ URL decoding failed: ${urlError.message}`);
    }
}

// Test with the actual token
async function testTokenDecryption() {
    console.log('\n🧪 TESTING TOKEN DECRYPTION WITH SERVER');
    console.log('─'.repeat(50));
    
    try {
        // Test with the provided token
        const result = await makeRequest('/partner-test/debug-auth', 'GET', null, `access_token=${ACCESS_TOKEN}`);
        
        console.log(`Status: ${result.status}`);
        console.log('Response:', JSON.stringify(result.data, null, 2));
        
        if (!result.success && result.status === 401) {
            console.log('\n❌ DECRYPTION FAILED - as expected based on previous tests');
        } else if (result.success) {
            console.log('\n✅ DECRYPTION SUCCEEDED - token is valid!');
        } else {
            console.log('\n❓ UNEXPECTED RESPONSE');
        }
        
    } catch (error) {
        console.log(`❌ Request failed: ${error.message}`);
    }
}

// Main analysis function
async function runAnalysis() {
    console.log('⏳ Waiting 5 seconds for server to be ready...\n');
    
    setTimeout(async () => {
        // Analyze token format
        analyzeTokenFormat();
        
        // Test with server
        await testTokenDecryption();
        
        // Provide analysis
        console.log('\n📊 ANALYSIS SUMMARY');
        console.log('═'.repeat(50));
        
        console.log('\n🔍 Token Format Analysis:');
        console.log('- The token appears to follow Rails MessageEncryptor format');
        console.log('- It uses the expected encrypted_data--iv--auth_tag structure');
        console.log('- Component lengths suggest AES-256-GCM encryption');
        
        console.log('\n🔧 Potential Issues:');
        console.log('1. ❌ Encryption key mismatch between auth service and support backend');
        console.log('2. ❌ Different encryption algorithm or parameters');
        console.log('3. ❌ Token was encrypted with different Rails secret key');
        console.log('4. ❌ Token format version incompatibility');
        
        console.log('\n✅ Recommended Solutions:');
        console.log('1. Verify ACCESS_TOKEN_ENCRYPTION_KEY matches auth service exactly');
        console.log('2. Check if auth service uses same AES-256-GCM parameters');
        console.log('3. Ensure Rails MessageEncryptor compatibility');
        console.log('4. Get a fresh token from auth service with current encryption key');
        
        console.log('\n🎯 Next Steps:');
        console.log('1. Compare encryption keys between services');
        console.log('2. Test with a freshly generated token');
        console.log('3. Verify encryption algorithm parameters match');
        console.log('4. Check Rails version compatibility');
        
        console.log('\n🏁 Analysis completed!');
        
    }, 5000);
}

// Run the analysis
runAnalysis().catch(console.error);
