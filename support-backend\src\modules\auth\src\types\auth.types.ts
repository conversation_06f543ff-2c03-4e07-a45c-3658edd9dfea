export interface User {
  id: string;
  sub?: string; // JWT subject (user ID)
  email?: string; // Made optional since it may not be present in JWT
  username?: string;
  firstName?: string;
  lastName?: string;
  role: string;
  permissions?: string[];
  createdAt: string;
  updatedAt: string;

  // Additional properties for compatibility with existing code
  phone?: string;
  password?: string;
  country?: string;
  verifiedEmail?: boolean;
  verifiedPhone?: boolean;
  totpSecret?: string;
  partnerId?: string;
  mfaEnabled?: boolean;
  active?: boolean;
  accountId?: string;
  isAdmin?: boolean;



  // External auth service fields
  accounts?: any[];
  partners?: any[];
  active_accounts?: any[];
  active_partners?: any[];
  first_name?: string;
  last_name?: string;

  // JWT-specific fields for tracking and debugging (all required from actual JWT)
  sessionId: string; // From JWT 'sid' field
  entitySet: string; // From JWT 'ent_set' field
  permissionVersion: number; // From JWT 'perm_v' field
  authMethods: string[]; // From JWT 'amr' field
  authTime: string; // From JWT 'auth_time' field (ISO string)
  jwtId: string; // From JWT 'jti' field
  authorizedParty: string; // From JWT 'azp' field

  // JWT metadata
  issuer: string; // From JWT 'iss' field
  audience: string | string[]; // From JWT 'aud' field
  expiresAt: string; // From JWT 'exp' field (ISO string)
}

export interface JWTPayload {
  // Standard JWT fields (as per Ruby payload structure)
  iss: string; // JWT issuer URL
  sub: string; // User ID (required)
  email?: string; // User email (may be missing in some JWTs)
  aud: string | string[]; // JWT audience
  exp: number; // Expiration time
  iat: number; // Issued at time
  jti: string; // JWT ID (UUID)

  // NGNair-specific fields (as per Ruby payload structure)
  sid: string; // Session ID in format "sess_{hex}"
  azp: string; // Authorized party (typically "webapp")
  ent_set: string; // Entity set for role/permission determination
  perm_v: number; // Permission version
  amr: string[]; // Authentication methods references (e.g., ["pwd"])
  auth_time: number; // Authentication time (Unix timestamp)

  // Legacy fields (for backward compatibility - may not be present)
  username?: string;
  firstName?: string;
  lastName?: string;
  role?: string;
  permissions?: string[];
  accounts?: any[];
  partners?: any[];
  active_accounts?: any[];
  active_partners?: any[];
  first_name?: string;
  last_name?: string;
}

export interface AuthConfig {
  authJwksUrl: string;
  encryptionKey: string;
  cookieNames: {
    accessToken: string;
    refreshToken: string;
  };
  // JWT verification options
  jwt?: {
    issuer?: string;
    audience?: string;
    skipVerification?: boolean;
  };
}

export interface DecryptedTokens {
  accessToken: string;
  refreshToken?: string;
}

export interface JWKSResponse {
  keys: Array<{
    kty: string;
    e: string;
    n: string;
    kid: string;
  }>;
}

export interface AuthenticatedRequest extends Request {
  user: User;
}

// Extend Express Request type globally
declare global {
  namespace Express {
    interface Request {
      user?: User;
    }
  }
}
