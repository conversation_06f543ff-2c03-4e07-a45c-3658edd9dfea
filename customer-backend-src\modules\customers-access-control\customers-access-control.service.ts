import { Injectable, Logger, ForbiddenException } from '@nestjs/common';
import { AuthUser } from '../auth-shared';
import { Prisma } from '@prisma/client';
import { CustomerWithRelations } from '../customers-query/customers-query.service';

export interface AccessControlOptions {
  requireAdmin?: boolean;
  requirePermission?: string;
  allowSelfAccess?: boolean;
  allowPartnerAccess?: boolean;
  allowAccountAccess?: boolean;
}

@Injectable()
export class CustomersAccessControlService {
  private readonly logger = new Logger(CustomersAccessControlService.name);

  /**
   * Check if user has permission to perform an action
   */
  checkPermission(user: AuthUser, permission: string): boolean {
    if (user.isAdmin || (user.permissions && user.permissions.includes('*'))) {
      return true;
    }
    return user.permissions ? user.permissions.includes(permission) : false;
  }

  /**
   * Check if user has admin access
   */
  checkAdminAccess(user: AuthUser): boolean {
    return user.isAdmin || false;
  }

  /**
   * Check if user can access a specific customer
   */
  checkCustomerAccess(
    user: AuthUser,
    customer: CustomerWithRelations,
    options: AccessControlOptions = {},
  ): boolean {
    // Admin users have access to everything
    if (user.isAdmin) {
      return true;
    }

    // Check if admin access is required
    if (options.requireAdmin) {
      return false;
    }

    // Check specific permission if required
    if (options.requirePermission && !this.checkPermission(user, options.requirePermission)) {
      return false;
    }

    // Check self access (if customer belongs to the user)
    if (options.allowSelfAccess && customer.externalId === user.id) {
      return true;
    }

    // Check partner access
    if (options.allowPartnerAccess && user.partnerId) {
      // Add logic to check if customer belongs to user's partner
      // This would depend on your data model
      return true;
    }

    // Check account access
    if (options.allowAccountAccess && user.accountId) {
      // Add logic to check if customer belongs to user's account
      // This would depend on your data model
      return true;
    }

    return false;
  }

  /**
   * Apply access control filters to Prisma where clause
   */
  applyAccessControlFilters(
    user: AuthUser,
    where: Prisma.CustomerWhereInput = {},
    options: AccessControlOptions = {},
  ): Prisma.CustomerWhereInput {
    // Admin users see everything
    if (user.isAdmin) {
      return where;
    }

    // Check if admin access is required
    if (options.requireAdmin) {
      // Return a filter that matches nothing
      return { ...where, id: 'impossible-id' };
    }

    // Check specific permission if required
    if (options.requirePermission && !this.checkPermission(user, options.requirePermission)) {
      return { ...where, id: 'impossible-id' };
    }

    const accessFilters: Prisma.CustomerWhereInput[] = [];

    // Add self access filter
    if (options.allowSelfAccess && user.id) {
      accessFilters.push({ externalId: user.id });
    }

    // Add partner access filter
    if (options.allowPartnerAccess && user.partnerId) {
      // Add logic based on your data model
      // For example, if customers have a partnerId field:
      // accessFilters.push({ partnerId: user.partnerId });
    }

    // Add account access filter
    if (options.allowAccountAccess && user.accountId) {
      // Add logic based on your data model
      // For example, if customers have an accountId field:
      // accessFilters.push({ accountId: user.accountId });
    }

    // If no access filters are applicable, deny access
    if (accessFilters.length === 0) {
      return { ...where, id: 'impossible-id' };
    }

    // Combine access filters with OR
    return {
      ...where,
      OR: accessFilters,
    };
  }

  /**
   * Validate and throw error if access is denied
   */
  validateCustomerAccess(
    user: AuthUser,
    customer: CustomerWithRelations,
    action: string,
    options: AccessControlOptions = {},
  ): void {
    if (!this.checkCustomerAccess(user, customer, options)) {
      this.logger.warn(
        `Access denied for user ${user.id} to ${action} customer ${customer.id}`,
      );
      throw new ForbiddenException(`Access denied to ${action} this customer`);
    }
  }

  /**
   * Validate permission and throw error if denied
   */
  validatePermission(user: AuthUser, permission: string, action: string): void {
    if (!this.checkPermission(user, permission)) {
      this.logger.warn(
        `Permission denied for user ${user.id} to ${action} (requires ${permission})`,
      );
      throw new ForbiddenException(`Permission denied to ${action}`);
    }
  }

  /**
   * Validate admin access and throw error if denied
   */
  validateAdminAccess(user: AuthUser, action: string): void {
    if (!this.checkAdminAccess(user)) {
      this.logger.warn(`Admin access denied for user ${user.id} to ${action}`);
      throw new ForbiddenException(`Admin access required to ${action}`);
    }
  }

  /**
   * Get default access control options for different operations
   */
  getDefaultOptions(operation: 'read' | 'write' | 'delete' | 'admin'): AccessControlOptions {
    switch (operation) {
      case 'read':
        return {
          requirePermission: 'read:customers',
          allowSelfAccess: true,
          allowPartnerAccess: true,
          allowAccountAccess: true,
        };
      case 'write':
        return {
          requirePermission: 'write:customers',
          allowSelfAccess: false,
          allowPartnerAccess: true,
          allowAccountAccess: true,
        };
      case 'delete':
        return {
          requirePermission: 'delete:customers',
          allowSelfAccess: false,
          allowPartnerAccess: false,
          allowAccountAccess: false,
        };
      case 'admin':
        return {
          requireAdmin: true,
        };
      default:
        return {};
    }
  }

  /**
   * Filter customer data based on user permissions
   */
  filterCustomerData(
    user: AuthUser,
    customer: CustomerWithRelations,
    options: AccessControlOptions = {},
  ): Partial<CustomerWithRelations> {
    // Admin users see everything
    if (user.isAdmin) {
      return customer;
    }

    // Create a copy of the customer data
    const filteredCustomer = { ...customer };

    // Remove sensitive fields based on permissions
    if (!this.checkPermission(user, 'read:customer:sensitive')) {
      filteredCustomer.taxId = null;
      filteredCustomer.notes = null;
    }

    if (!this.checkPermission(user, 'read:customer:contacts')) {
      filteredCustomer.contacts = [];
    }

    if (!this.checkPermission(user, 'read:customer:addresses')) {
      filteredCustomer.addresses = [];
    }

    if (!this.checkPermission(user, 'read:customer:preferences')) {
      filteredCustomer.preferences = [];
    }

    return filteredCustomer;
  }

  /**
   * Check if user can perform bulk operations
   */
  checkBulkOperationAccess(user: AuthUser, operation: 'update' | 'delete'): boolean {
    if (user.isAdmin) {
      return true;
    }

    const permission = operation === 'update' ? 'bulk:update:customers' : 'bulk:delete:customers';
    return this.checkPermission(user, permission);
  }

  /**
   * Validate bulk operation access
   */
  validateBulkOperationAccess(user: AuthUser, operation: 'update' | 'delete'): void {
    if (!this.checkBulkOperationAccess(user, operation)) {
      this.logger.warn(
        `Bulk ${operation} access denied for user ${user.id}`,
      );
      throw new ForbiddenException(`Permission denied for bulk ${operation} operations`);
    }
  }
}
