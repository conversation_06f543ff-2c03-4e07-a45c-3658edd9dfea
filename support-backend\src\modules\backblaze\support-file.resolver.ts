import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { FileService } from './file.service';
import { SupportFile } from './support-file.model';
import { GraphQLAuthGuard } from '../auth/src/guards';
import { GraphQLUpload, FileUpload } from 'graphql-upload-minimal';

@Resolver(() => SupportFile)
export class SupportFileResolver {
  constructor(private readonly fileService: FileService) {}

  @Query(() => SupportFile, { name: 'supportFile' })
  @UseGuards(GraphQLAuthGuard)
  async supportFile(@Args('id', { type: () => String }) id: string) {
    return this.fileService.findById(id);
  }

  @Query(() => [SupportFile], { name: 'supportFiles' })
  @UseGuards(GraphQLAuthGuard)
  async supportFiles(
    @Args('ticketId', { type: () => String, nullable: true }) ticketId?: string,
    @Args('commentId', { type: () => String, nullable: true }) commentId?: string
  ) {
    return this.fileService.getFiles(ticketId, commentId);
  }

  @Mutation(() => SupportFile)
  @UseGuards(GraphQLAuthGuard)
  async uploadSupportFile(
    @Args({ name: 'file', type: () => GraphQLUpload }) file: FileUpload,
    @Args('ticketId', { type: () => String, nullable: true }) ticketId?: string,
    @Args('commentId', { type: () => String, nullable: true }) commentId?: string,
    @Context() context?: any
  ) {
    const user = context.req.user;
    console.log('uploadSupportFile input data:', { ticketId, commentId }, 'user:', user?.id);

    // Convert FileUpload to FastifyFileUpload format
    const stream = file.createReadStream();
    const chunks: Buffer[] = [];

    return new Promise((resolve, reject) => {
      stream.on('data', (chunk: Buffer) => chunks.push(chunk));
      stream.on('end', async () => {
        try {
          const fileBuffer = Buffer.concat(chunks);
          const fastifyFile = {
            fieldname: 'file',
            originalname: file.filename,
            encoding: file.encoding || 'utf-8',
            mimetype: file.mimetype,
            buffer: fileBuffer,
            size: fileBuffer.length,
          };

          const result = await this.fileService.uploadFile(fastifyFile, user.id, ticketId, commentId);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });
      stream.on('error', reject);
    });
  }

  @Mutation(() => SupportFile)
  @UseGuards(GraphQLAuthGuard)
  async deleteSupportFile(@Args('id', { type: () => String }) id: string) {
    return this.fileService.deleteFile(id);
  }
}
