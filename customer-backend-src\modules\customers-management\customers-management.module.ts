import { Module } from '@nestjs/common';
import { CustomersManagementService } from './customers-management.service';
import { CustomersManagementController } from './customers-management.controller';
import { PrismaModule } from '../../prisma/prisma.module';
import { ConfigModule } from '../../config/config.module';
import { CustomersQueryModule } from '../customers-query/customers-query.module';

@Module({
  imports: [
    PrismaModule,
    ConfigModule,
    CustomersQueryModule,
  ],
  controllers: [
    CustomersManagementController,
  ],
  providers: [
    CustomersManagementService,
  ],
  exports: [
    CustomersManagementService,
  ],
})
export class CustomersManagementModule {}
