import {
  Controller,
  Post,
  Body,
  UseGuards,
  Logger,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ApiGuard, getCurrentUser, AuthUser } from '../auth-shared';
import { TransactionService } from './transaction.service';
import {
  TransactionRequestDto,
  OtpVerificationRequestDto,
  TransactionResponseDto,
} from './dto/transaction.dto';

@ApiTags('transactions')
@Controller('transactions')
@UseGuards(ApiGuard)
@ApiBearerAuth()
export class TransactionController {
  private readonly logger = new Logger(TransactionController.name);

  constructor(private readonly transactionService: TransactionService) {}

  @Post('initiate')
  @ApiOperation({ 
    summary: 'Initiate a transaction and send OTP',
    description: 'Starts a new transaction process by validating the request and sending an OTP to the user\'s registered phone number. Returns user details with nested phone information.'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Transaction initiated successfully and OTP sent',
    type: TransactionResponseDto,
    examples: {
      success: {
        summary: 'Successful transaction initiation',
        value: {
          transactionId: 'txn_**********abcdef',
          status: 'otp_sent',
          message: 'OTP sent to +***-***-1234',
          userDetails: {
            id: 'cmca6q7w40000l9015jo4lc5o',
            email: '<EMAIL>',
            firstName: 'John',
            lastName: 'Doe',
            phone: {
              number: '+**********',
              masked: '+***-***-7890'
            },
            country: 'USA',
            verifiedEmail: false,
            verifiedPhone: false,
            role: 'account_user',
            isAdmin: false,
            permissions: ['read:customers', 'write:customers']
          }
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid transaction data or missing phone number',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Phone number is required for transaction processing' },
        error: { type: 'string', example: 'Bad Request' },
        statusCode: { type: 'number', example: 400 }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid or missing authentication token',
  })
  async initiateTransaction(
    @getCurrentUser() user: AuthUser,
    @Body() transactionData: TransactionRequestDto,
  ): Promise<TransactionResponseDto> {
    this.logger.log(`REST: Initiating transaction for user: ${user.email}`);

    const result = await this.transactionService.initiateTransaction(user, transactionData);

    return {
      transactionId: result.transactionId,
      status: result.status,
      message: result.message,
      userDetails: result.userDetails ? {
        id: result.userDetails.id,
        email: result.userDetails.email,
        firstName: result.userDetails.firstName,
        lastName: result.userDetails.lastName,
        phone: result.userDetails.phone,
        country: result.userDetails.country,
        verifiedEmail: result.userDetails.verifiedEmail,
        verifiedPhone: result.userDetails.verifiedPhone,
        role: result.userDetails.role,
        createdAt: result.userDetails.createdAt,
        partnerId: Array.isArray(result.userDetails.partnerId) 
          ? result.userDetails.partnerId.join(',') 
          : result.userDetails.partnerId,
        mfaEnabled: result.userDetails.mfaEnabled,
        active: result.userDetails.active,
        accountId: result.userDetails.accountId,
        isAdmin: result.userDetails.isAdmin,
        permissions: result.userDetails.permissions,
      } : undefined,
      paymentDetails: result.paymentDetails,
    };
  }

  @Post('verify-otp')
  @ApiOperation({ 
    summary: 'Verify OTP and complete transaction',
    description: 'Verifies the OTP code and completes the transaction. Returns payment details (routing number, CVV) upon successful verification. Demo OTP: "123456"'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'OTP verified successfully and transaction completed',
    type: TransactionResponseDto,
    examples: {
      success: {
        summary: 'Successful OTP verification',
        value: {
          transactionId: 'txn_**********abcdef',
          status: 'completed',
          message: 'Transaction completed successfully',
          userDetails: {
            id: 'cmca6q7w40000l9015jo4lc5o',
            email: '<EMAIL>',
            firstName: 'John',
            lastName: 'Doe',
            phone: {
              number: '+**********',
              masked: '+***-***-7890'
            },
            country: 'USA',
            isAdmin: false,
            permissions: ['read:customers', 'write:customers']
          },
          paymentDetails: {
            routingNumber: '*********',
            cvv: '123',
            accountNumber: '****1234',
            expiryDate: '12/25'
          }
        }
      },
      failed: {
        summary: 'Failed OTP verification',
        value: {
          transactionId: 'txn_**********abcdef',
          status: 'failed',
          message: 'Invalid OTP. Please try again.'
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid OTP or transaction data',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Phone number does not match user account or invalid token',
  })
  async verifyOtpAndCompleteTransaction(
    @getCurrentUser() user: AuthUser,
    @Body() otpData: OtpVerificationRequestDto,
  ): Promise<TransactionResponseDto> {
    this.logger.log(`REST: Verifying OTP for transaction: ${otpData.transactionId}`);

    const result = await this.transactionService.verifyOtpAndCompleteTransaction(user, otpData);

    return {
      transactionId: result.transactionId,
      status: result.status,
      message: result.message,
      userDetails: result.userDetails ? {
        id: result.userDetails.id,
        email: result.userDetails.email,
        firstName: result.userDetails.firstName,
        lastName: result.userDetails.lastName,
        phone: result.userDetails.phone,
        country: result.userDetails.country,
        verifiedEmail: result.userDetails.verifiedEmail,
        verifiedPhone: result.userDetails.verifiedPhone,
        role: result.userDetails.role,
        createdAt: result.userDetails.createdAt,
        partnerId: Array.isArray(result.userDetails.partnerId) 
          ? result.userDetails.partnerId.join(',') 
          : result.userDetails.partnerId,
        mfaEnabled: result.userDetails.mfaEnabled,
        active: result.userDetails.active,
        accountId: result.userDetails.accountId,
        isAdmin: result.userDetails.isAdmin,
        permissions: result.userDetails.permissions,
      } : undefined,
      paymentDetails: result.paymentDetails,
    };
  }
}
