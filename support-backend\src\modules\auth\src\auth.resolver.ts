import { Resolver, Query, Args, Context } from '@nestjs/graphql';
import { Logger, UseGuards } from '@nestjs/common';
import { AuthSharedService } from './auth.service';
import { GraphQLAuthGuard } from './guards/graphql-auth.guard';
import { JWTPayloadGraphQL, UserGraphQL } from './types/graphql.types';

@Resolver()
export class AuthResolver {
  private readonly logger = new Logger(AuthResolver.name);

  constructor(private readonly authService: AuthSharedService) {}

  @Query(() => JWTPayloadGraphQL, { 
    name: 'authMe', 
    description: 'Get current user JWT payload from authentication service - returns only actual JWT fields' 
  })
  async getCurrentUser(@Context() context: any): Promise<any> {
    this.logger.log('🔍 [AUTH RESOLVER] authMe query called');

    const request = context.req;

    if (!request) {
      this.logger.error('❌ [AUTH RESOLVER] Request context not available');
      throw new Error('Request context not available');
    }

    // Extract cookies from request
    const cookies = request.cookies || {};
    this.logger.log(`🍪 [AUTH RESOLVER] Cookies available: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    if (!cookies.access_token) {
      this.logger.warn('❌ [AUTH RESOLVER] No access token found in cookies');
      throw new Error('Authentication required - no access token');
    }

    try {
      this.logger.log('🔐 [AUTH RESOLVER] Authenticating user and extracting JWT payload...');

      // Use the corrected method name from support-backend
      const jwtPayload = await this.authService.authenticateUserFromCookies(cookies);

      this.logger.log(`✅ [AUTH RESOLVER] JWT payload retrieved successfully for user: ${jwtPayload.sub}`);
      this.logger.log(`📋 [AUTH RESOLVER] JWT payload fields: ${Object.keys(jwtPayload).join(', ')}`);
      
      return jwtPayload;
    } catch (error) {
      this.logger.error(`❌ [AUTH RESOLVER] Error getting current user: ${error.message}`);
      throw error;
    }
  }

  @Query(() => [UserGraphQL], { 
    name: 'authUsers', 
    description: 'Get all users from external auth service' 
  })
  @UseGuards(GraphQLAuthGuard)
  async getAllUsers(@Context() context: any): Promise<any[]> {
    this.logger.log('🔍 [AUTH RESOLVER] authUsers query called');

    const request = context.req;

    if (!request) {
      this.logger.error('❌ [AUTH RESOLVER] Request context not available');
      throw new Error('Request context not available');
    }

    // Extract cookies from request
    const cookies = request.cookies || {};
    this.logger.log(`🍪 [AUTH RESOLVER] Cookies available: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    if (!cookies.access_token) {
      this.logger.warn('❌ [AUTH RESOLVER] No access token found in cookies');
      throw new Error('Authentication required - no access token');
    }

    try {
      this.logger.log('🔐 [AUTH RESOLVER] Fetching users from external auth service...');

      const users = await this.authService.getAllUsers(cookies);

      this.logger.log(`✅ [AUTH RESOLVER] Users retrieved successfully: ${Array.isArray(users) ? users.length : 'unknown'} users`);

      return users;
    } catch (error) {
      this.logger.error(`❌ [AUTH RESOLVER] Error getting all users: ${error.message}`);
      throw error;
    }
  }

  @Query(() => UserGraphQL, { 
    name: 'authUser', 
    description: 'Get specific user by ID from external auth service' 
  })
  @UseGuards(GraphQLAuthGuard)
  async getUserById(
    @Args('id', { type: () => String }) id: string,
    @Context() context: any
  ): Promise<any> {
    this.logger.log(`🔍 [AUTH RESOLVER] authUser query called for ID: ${id}`);

    const request = context.req;

    if (!request) {
      this.logger.error('❌ [AUTH RESOLVER] Request context not available');
      throw new Error('Request context not available');
    }

    // Extract cookies from request
    const cookies = request.cookies || {};
    this.logger.log(`🍪 [AUTH RESOLVER] Cookies available: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    if (!cookies.access_token) {
      this.logger.warn('❌ [AUTH RESOLVER] No access token found in cookies');
      throw new Error('Authentication required - no access token');
    }

    try {
      this.logger.log(`🔐 [AUTH RESOLVER] Fetching user ${id} from external auth service...`);

      const user = await this.authService.getUserById(id, cookies);

      this.logger.log(`✅ [AUTH RESOLVER] User ${id} retrieved successfully`);

      return user;
    } catch (error) {
      this.logger.error(`❌ [AUTH RESOLVER] Error getting user ${id}: ${error.message}`);
      throw error;
    }
  }

  @Query(() => String, { 
    name: 'authPing', 
    description: 'Simple ping endpoint to test authentication GraphQL setup' 
  })
  async authPing(): Promise<string> {
    this.logger.log('🔍 [AUTH RESOLVER] authPing query called');
    return 'Auth GraphQL resolver is working!';
  }
}
