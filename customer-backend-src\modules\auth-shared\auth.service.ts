import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '../../config/config.service';
import * as crypto from 'crypto';
import { User, JWTPayload, AuthConfig, DecryptedTokens, DEFAULT_AUTH_CONFIG } from './types/auth.types';
import { firstValueFrom } from 'rxjs';

// JWKS client for JWT verification - temporarily disabled for testing
// const jwksClient = require('jwks-client');

@Injectable()
export class AuthSharedService {
  private readonly logger = new Logger(AuthSharedService.name);
  private readonly config: AuthConfig;
  // private jwksClientInstance: any; // Temporarily disabled

  constructor(
    private configService: ConfigService,
    private httpService: HttpService
  ) {
    // Merge with default configuration
    this.config = {
      ...DEFAULT_AUTH_CONFIG,
      encryptionKey: this.configService.get('ACCESS_TOKEN_ENCRYPTION_KEY') || 'b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8',
      authJwksUrl: this.configService.get('AUTH_JWKS_URL') || 'https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks',
      externalAuthServiceUrl: process.env.EXTERNAL_AUTH_SERVICE_URL || 'https://ng-auth-dev.dev1.ngnair.com/api',
      cookieNames: {
        accessToken: process.env.ACCESS_TOKEN_COOKIE_NAME || 'access_token',
        refreshToken: process.env.REFRESH_TOKEN_COOKIE_NAME || 'refresh_token',
      },
      requestTimeout: parseInt(process.env.AUTH_REQUEST_TIMEOUT || '10000'),
      enableDebugLogging: process.env.AUTH_DEBUG_LOGGING === 'true',
      jwt: {
        issuer: process.env.JWT_ISSUER,
        audience: process.env.JWT_AUDIENCE,
        skipVerification: process.env.JWT_SKIP_VERIFICATION === 'true',
      },
    };

    this.logger.log(`🔧 [AUTH SERVICE] Configuration initialized:`);
    this.logger.log(`🔧 [AUTH SERVICE] - JWKS URL: ${this.config.authJwksUrl}`);
    this.logger.log(`🔧 [AUTH SERVICE] - External auth service: ${this.config.externalAuthServiceUrl}`);
    this.logger.log(`🔧 [AUTH SERVICE] - Request timeout: ${this.config.requestTimeout}ms`);
    this.logger.log(`🔧 [AUTH SERVICE] - Debug logging: ${this.config.enableDebugLogging}`);
    this.logger.log(`🔧 [AUTH SERVICE] - JWT skip verification: ${this.config.jwt?.skipVerification || false}`);
  }

  /**
   * Decrypt cookies using Rails-compatible AES-256-GCM encryption
   * Follows the exact 4-step sequence: URL decode -> Base64 decode -> Parse Rails format -> AES-256-GCM decrypt
   */
  private async decryptCookie(encryptedValue: string): Promise<string> {
    try {
      this.logger.log(`🔓 [AUTH SERVICE] Starting Rails-compatible decryption process...`);
      this.logger.log(`🔐 [AUTH SERVICE] Encrypted value length: ${encryptedValue.length}`);
      this.logger.log(`🔑 [AUTH SERVICE] Using encryption key: ${this.config.encryptionKey.substring(0, 16)}...`);

      // Step 1: URL decode the cookie value
      this.logger.log(`🔐 [AUTH SERVICE] Step 1: URL decoding...`);
      const urlDecoded = decodeURIComponent(encryptedValue);
      this.logger.log(`✅ [AUTH SERVICE] URL decoded successfully, length: ${urlDecoded.length}`);

      // Step 2: Base64 decode to get Rails MessageEncryptor format
      this.logger.log(`🔐 [AUTH SERVICE] Step 2: Base64 decoding...`);
      const base64Decoded = Buffer.from(urlDecoded, 'base64');
      this.logger.log(`✅ [AUTH SERVICE] Base64 decoded successfully, length: ${base64Decoded.length}`);

      // Step 3: Parse Rails MessageEncryptor format: encrypted_data--iv--auth_tag
      this.logger.log(`🔐 [AUTH SERVICE] Step 3: Parsing Rails MessageEncryptor format...`);
      const base64String = base64Decoded.toString('utf8');
      const parts = base64String.split('--');
      this.logger.log(`🔐 [AUTH SERVICE] Split into ${parts.length} parts using Rails format (--)`);

      if (parts.length !== 3) {
        this.logger.error(`❌ [AUTH SERVICE] Invalid encrypted cookie format. Expected 3 parts, got ${parts.length}`);
        this.logger.error(`🔐 [AUTH SERVICE] First 100 chars of decoded value: ${base64String.substring(0, 100)}`);
        throw new Error('Invalid encrypted cookie format');
      }

      // Decode each part from base64
      const encryptedData = Buffer.from(parts[0], 'base64');
      const iv = Buffer.from(parts[1], 'base64');
      const authTag = Buffer.from(parts[2], 'base64');

      this.logger.log(`🔐 [AUTH SERVICE] Encrypted data length: ${encryptedData.length}, IV length: ${iv.length}, Auth tag length: ${authTag.length}`);

      // Step 4: AES-256-GCM decryption
      this.logger.log(`🔐 [AUTH SERVICE] Step 4: AES-256-GCM decryption...`);

      // Convert hex key to buffer (ensure it's exactly 32 bytes for AES-256)
      const key = Buffer.from(this.config.encryptionKey, 'hex');
      this.logger.log(`🔑 [AUTH SERVICE] Key length: ${key.length} bytes`);

      if (key.length !== 32) {
        throw new Error(`Invalid encryption key length. Expected 32 bytes, got ${key.length}`);
      }

      // Create decipher for GCM mode
      const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv);
      decipher.setAuthTag(authTag);

      // Decrypt
      let decrypted = decipher.update(encryptedData, undefined, 'utf8');
      decrypted += decipher.final('utf8');

      this.logger.log(`✅ [AUTH SERVICE] Decryption successful, result length: ${decrypted.length}`);

      // Remove quotes if present (Rails adds quotes around JSON strings)
      const result = decrypted.replace(/^"(.*)"$/, '$1');
      this.logger.log(`✅ [AUTH SERVICE] Final result length after quote removal: ${result.length}`);
      this.logger.log(`🎯 [AUTH SERVICE] Decrypted token (first 100 chars): ${result.substring(0, 100)}...`);

      return result;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Decryption failed: ${error.message}`);
      this.logger.error(`❌ [AUTH SERVICE] Error stack: ${error.stack}`);
      throw new UnauthorizedException('Failed to decrypt authentication token');
    }
  }

  /**
   * Extract raw encrypted tokens from cookies (without decryption)
   */
  extractRawTokensFromCookies(cookies: Record<string, string>): { accessToken?: string; refreshToken?: string } {
    const encryptedAccessToken = cookies[this.config.cookieNames.accessToken];
    const encryptedRefreshToken = cookies[this.config.cookieNames.refreshToken];

    this.logger.log(`Available cookies: ${Object.keys(cookies)}`);
    this.logger.log(`Looking for access token: ${this.config.cookieNames.accessToken}`);
    this.logger.log(`Looking for refresh token: ${this.config.cookieNames.refreshToken}`);
    this.logger.log(`Access token found: ${!!encryptedAccessToken}`);
    this.logger.log(`Refresh token found: ${!!encryptedRefreshToken}`);

    // URL decode the tokens since cookies are automatically URL-encoded by browsers
    const decodedAccessToken = encryptedAccessToken ? decodeURIComponent(encryptedAccessToken) : undefined;
    const decodedRefreshToken = encryptedRefreshToken ? decodeURIComponent(encryptedRefreshToken) : undefined;

    if (encryptedAccessToken && decodedAccessToken) {
      this.logger.log(`🔗 [AUTH SERVICE] URL-encoded token length: ${encryptedAccessToken.length} characters`);
      this.logger.log(`🔓 [AUTH SERVICE] URL-decoded token length: ${decodedAccessToken.length} characters`);
      this.logger.log(`🔓 [AUTH SERVICE] Decoded token preview: ${decodedAccessToken.substring(0, 50)}...`);
    }

    return {
      accessToken: decodedAccessToken,
      refreshToken: decodedRefreshToken
    };
  }

  /**
   * Extract and decrypt tokens from cookies
   */
  async extractTokensFromCookies(cookies: Record<string, string>): Promise<DecryptedTokens> {
    const encryptedAccessToken = cookies[this.config.cookieNames.accessToken];
    const encryptedRefreshToken = cookies[this.config.cookieNames.refreshToken];

    this.logger.log(`Available cookies: ${Object.keys(cookies)}`);
    this.logger.log(`Looking for access token: ${this.config.cookieNames.accessToken}`);
    this.logger.log(`Looking for refresh token: ${this.config.cookieNames.refreshToken}`);
    this.logger.log(`Access token found: ${!!encryptedAccessToken}`);
    this.logger.log(`Refresh token found: ${!!encryptedRefreshToken}`);

    if (!encryptedAccessToken) {
      throw new UnauthorizedException('Missing access token');
    }

    if (!encryptedRefreshToken) {
      this.logger.warn('Refresh token not found, proceeding with access token only');
    }

    try {
      const accessToken = await this.decryptCookie(encryptedAccessToken);
      const refreshToken = encryptedRefreshToken ? await this.decryptCookie(encryptedRefreshToken) : undefined;

      return { accessToken, refreshToken };
    } catch (error) {
      this.logger.error('Failed to decrypt tokens:', error);
      throw new UnauthorizedException('Invalid authentication tokens');
    }
  }



  /**
   * Verify JWT token and extract payload
   * JWKS verification temporarily disabled - extracts payload directly
   */
  async verifyToken(token: string): Promise<JWTPayload> {
    try {
      this.logger.log(`🔍 [AUTH SERVICE] Starting JWT verification...`);
      this.logger.log(`🎫 [AUTH SERVICE] Token length: ${token.length}`);

      // Parse JWT without verification (JWKS client disabled)
      const parts = token.split('.');
      if (parts.length !== 3) {
        this.logger.error(`❌ [AUTH SERVICE] Invalid JWT format. Expected 3 parts, got ${parts.length}`);
        throw new Error('Invalid JWT format');
      }

      // Decode payload
      const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString('utf8')) as JWTPayload;

      // Basic validation for required JWT claims
      if (!payload.sub || !payload.email || !payload.iss || !payload.aud || !payload.exp || !payload.iat) {
        this.logger.error(`❌ [AUTH SERVICE] Invalid JWT payload - missing required fields`);
        this.logger.error(`❌ [AUTH SERVICE] Required fields: sub, email, iss, aud, exp, iat`);
        this.logger.error(`❌ [AUTH SERVICE] Received: ${JSON.stringify(Object.keys(payload))}`);
        throw new Error('Invalid JWT payload - missing required fields');
      }

      // Validate token expiration
      const now = Math.floor(Date.now() / 1000);
      if (payload.exp <= now) {
        this.logger.error(`❌ [AUTH SERVICE] JWT token has expired. Exp: ${payload.exp}, Now: ${now}`);
        throw new Error('JWT token has expired');
      }

      // Validate issuer if configured
      if (this.config.jwt?.issuer && payload.iss !== this.config.jwt.issuer) {
        this.logger.error(`❌ [AUTH SERVICE] Invalid JWT issuer. Expected: ${this.config.jwt.issuer}, Got: ${payload.iss}`);
        throw new Error('Invalid JWT issuer');
      }

      // Validate audience if configured
      if (this.config.jwt?.audience) {
        const audiences = Array.isArray(payload.aud) ? payload.aud : [payload.aud];
        if (!audiences.includes(this.config.jwt.audience)) {
          this.logger.error(`❌ [AUTH SERVICE] Invalid JWT audience. Expected: ${this.config.jwt.audience}, Got: ${JSON.stringify(payload.aud)}`);
          throw new Error('Invalid JWT audience');
        }
      }

      this.logger.log(`✅ [AUTH SERVICE] JWT verification successful for user ID: ${payload.sub}`);

      return payload;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] JWT verification failed: ${error.message}`);
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

  /**
   * Get user from JWT payload
   */
  /**
   * Get user from JWT payload - returns ONLY actual JWT fields (no computed/hardcoded fields)
   * This matches the auth-integration-package implementation
   */
  getUserFromPayload(payload: JWTPayload): any {
    // Validate required JWT fields based on actual Rails payload structure
    if (!payload.sub || !payload.email || !payload.iss || !payload.aud || !payload.exp || !payload.iat) {
      this.logger.error(`❌ [AUTH SERVICE] Missing required JWT fields. Payload: ${JSON.stringify(payload)}`);
      throw new UnauthorizedException('Invalid JWT payload - missing required fields');
    }

    this.logger.log(`📋 [AUTH SERVICE] Processing JWT for user: ${payload.sub}`);
    this.logger.log(`📋 [AUTH SERVICE] Email: ${payload.email}`);
    this.logger.log(`📋 [AUTH SERVICE] Session ID: ${payload.sid || 'not present'}`);
    this.logger.log(`📋 [AUTH SERVICE] Returning ONLY actual JWT payload fields (no computed fields)`);

    // Return ONLY the actual fields from the JWT payload - no computed, derived, or hardcoded fields
    const actualJwtFields: any = {
      // Standard JWT fields (exactly as they appear in the JWT)
      iss: payload.iss,
      sub: payload.sub,
      aud: payload.aud,
      exp: payload.exp,
      iat: payload.iat,
      jti: payload.jti,
      email: payload.email,

      // NGNair-specific fields (exactly as they appear in the JWT, if present)
      ...(payload.sid && { sid: payload.sid }),
      ...(payload.azp && { azp: payload.azp }),
      ...(payload.ent_set !== undefined && { ent_set: payload.ent_set }),
      ...(payload.perm_v !== undefined && { perm_v: payload.perm_v }),
      ...(payload.amr && { amr: payload.amr }),
      ...(payload.auth_time && { auth_time: payload.auth_time }),

      // Optional user fields (only if present in JWT)
      ...(payload.first_name && { first_name: payload.first_name }),
      ...(payload.last_name && { last_name: payload.last_name }),
    };

    this.logger.log(`📋 [AUTH SERVICE] Actual JWT fields being returned: ${JSON.stringify(Object.keys(actualJwtFields))}`);

    return actualJwtFields;
  }

  /**
   * Authenticate user from cookies
   */
  async authenticateFromCookies(cookies: Record<string, string>): Promise<any> {
    this.logger.log('🔐 [AUTH SERVICE] Starting authentication from cookies');
    this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    try {
      // Extract and decrypt tokens
      this.logger.log('🔓 [AUTH SERVICE] Extracting and decrypting tokens...');
      const { accessToken } = await this.extractTokensFromCookies(cookies);
      this.logger.log(`🔑 [AUTH SERVICE] Access token extracted: ${accessToken ? 'YES' : 'NO'}`);

      // Verify access token
      this.logger.log('✅ [AUTH SERVICE] Verifying access token...');
      const payload = await this.verifyToken(accessToken);
      this.logger.log(`👤 [AUTH SERVICE] Token verified, user ID: ${payload.sub}`);

      // Return JWT payload directly as per Rails specification
      this.logger.log('📋 [AUTH SERVICE] Returning JWT payload...');
      const jwtResponse = this.getUserFromPayload(payload);
      this.logger.log(`✅ [AUTH SERVICE] Authentication successful: ${jwtResponse.email}`);

      return jwtResponse;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Authentication failed: ${error.message}`);
      this.logger.error(`❌ [AUTH SERVICE] Error stack: ${error.stack}`);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  /**
   * Refresh authentication using refresh token
   */
  async refreshAuthentication(cookies: Record<string, string>): Promise<boolean> {
    try {
      // Extract and decrypt tokens
      const { refreshToken } = await this.extractTokensFromCookies(cookies);

      if (!refreshToken) {
        throw new UnauthorizedException('Refresh token not available');
      }

      // Verify refresh token
      await this.verifyToken(refreshToken);

      // In a real implementation, you would call the auth service to get new tokens
      // and set new cookies. For now, we just verify that the refresh token is valid
      return true;
    } catch (error) {
      this.logger.error('Token refresh failed:', error);
      return false;
    }
  }

  /**
   * Check if user has required permissions
   */
  hasPermissions(user: User, requiredPermissions: string[]): boolean {
    if (!user.permissions) return false;
    return requiredPermissions.every(permission => user.permissions!.includes(permission));
  }

  /**
   * Check if user has required role
   */
  hasRole(user: User, requiredRole: string): boolean {
    return user.role === requiredRole;
  }

  /**
   * Authenticate user from cookies by calling external auth service
   */
  async authenticateFromCookiesViaExternalService(cookies: Record<string, string>): Promise<User> {
    this.logger.log('🔐 [AUTH SERVICE] Starting authentication from cookies via external service');
    this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    try {
      // Extract raw encrypted access token from cookies
      const { accessToken: encryptedAccessToken } = this.extractRawTokensFromCookies(cookies);

      if (!encryptedAccessToken) {
        throw new UnauthorizedException('Missing access token');
      }

      // Get auth service URL from config
      const authServiceUrl = this.configService.get('AUTH_SERVICE_URL') || 'https://ng-auth-dev.dev1.ngnair.com';
      const url = `${authServiceUrl}/api/v1/users`;

      this.logger.log(`🌐 [AUTH SERVICE] Making request to: ${url}`);
      this.logger.log(`🍪 [AUTH SERVICE] Forwarding cookie: access_token=${encryptedAccessToken.substring(0, 50)}...`);

      // Prepare headers for external service request
      const requestHeaders = {
        'Cookie': `access_token=${encryptedAccessToken}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Customer-Service/1.0',
      };

      // Log complete request details for debugging
      this.logger.log(`📋 [AUTH SERVICE] Complete request headers:`);
      this.logger.log(`   - Cookie: ${requestHeaders.Cookie.substring(0, 100)}...`);
      this.logger.log(`   - Content-Type: ${requestHeaders['Content-Type']}`);
      this.logger.log(`   - User-Agent: ${requestHeaders['User-Agent']}`);
      this.logger.log(`🔍 [AUTH SERVICE] Full encrypted token length: ${encryptedAccessToken.length} characters`);

      // Forward the encrypted access_token as a cookie to external service
      const response = await firstValueFrom(
        this.httpService.get(url, {
          headers: requestHeaders,
          timeout: 10000,
        })
      );

      this.logger.log(`✅ [AUTH SERVICE] User authenticated via external service`);
      this.logger.log(`📄 [AUTH SERVICE] Response data: ${JSON.stringify(response.data)}`);

      // Return the first user from the response (assuming it's an array)
      const userData = Array.isArray(response.data) ? response.data[0] : response.data;
      return userData;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] External authentication failed: ${error.message}`);
      if (error.response?.status === 401) {
        throw new UnauthorizedException('Invalid or expired access token');
      }
      throw new UnauthorizedException('Authentication failed');
    }
  }

  /**
   * Manually set access token cookie for debugging purposes
   */
  setAccessTokenCookie(response: any, token: string): boolean {
    try {
      this.logger.log(`🍪 [AUTH SERVICE] Setting access token cookie manually`);
      this.logger.log(`🔐 [AUTH SERVICE] Token length: ${token.length} characters`);
      this.logger.log(`🔐 [AUTH SERVICE] Token preview: ${token.substring(0, 50)}...`);

      // URL encode the token value
      const encodedToken = encodeURIComponent(token);
      this.logger.log(`🔗 [AUTH SERVICE] URL encoded token length: ${encodedToken.length} characters`);

      // Set cookie with specified parameters
      const cookieOptions = [
        `access_token=${encodedToken}`,
        'Domain=.dev1.ngnair.com',
        'Path=/',
        'Max-Age=900', // 15 minutes
        'SameSite=Lax'
      ].join('; ');

      response.setHeader('Set-Cookie', cookieOptions);
      this.logger.log(`✅ [AUTH SERVICE] Cookie set successfully: ${cookieOptions.substring(0, 100)}...`);

      return true;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Failed to set cookie: ${error.message}`);
      return false;
    }
  }

  /**
   * Decrypt token for testing purposes (public method)
   */
  async decryptTokenForTesting(encryptedToken: string): Promise<string> {
    return await this.decryptCookie(encryptedToken);
  }



  /**
   * Authenticate user from cookies using proper external auth service integration
   * This is the main method for the /auth/me endpoint
   */
  async authenticateUserFromCookies(cookies: Record<string, string>): Promise<any> {
    this.logger.log('🔐 [AUTH SERVICE] Starting authentication for /auth/me endpoint');
    this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    try {
      // Extract encrypted access token from cookies
      const encryptedAccessToken = cookies[this.config.cookieNames.accessToken];

      if (!encryptedAccessToken) {
        throw new UnauthorizedException('Missing access token');
      }

      this.logger.log(`🔑 [AUTH SERVICE] Found encrypted access token: ${encryptedAccessToken.substring(0, 50)}...`);

      // Step 1: Decrypt the access token
      this.logger.log('🔓 [AUTH SERVICE] Step 1: Decrypting access token...');
      const decryptedToken = await this.decryptCookie(encryptedAccessToken);
      this.logger.log(`✅ [AUTH SERVICE] Token decrypted successfully, length: ${decryptedToken.length}`);

      // Step 2: Verify JWT using JWKS
      this.logger.log('🔍 [AUTH SERVICE] Step 2: Verifying JWT with JWKS...');
      const payload = await this.verifyToken(decryptedToken);
      this.logger.log(`✅ [AUTH SERVICE] JWT verified successfully for user: ${payload.sub}`);

      // Step 3: Return JWT payload directly as per Rails specification
      this.logger.log('👤 [AUTH SERVICE] Step 3: Returning JWT payload...');
      const jwtResponse = this.getUserFromPayload(payload);
      this.logger.log(`✅ [AUTH SERVICE] User authenticated successfully: ${jwtResponse.email}`);

      return jwtResponse;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Authentication failed: ${error.message}`);
      this.logger.error(`❌ [AUTH SERVICE] Error stack: ${error.stack}`);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  /**
   * Get user by ID from external auth service
   */
  async getUserById(userId: string, cookies: Record<string, string>): Promise<User> {
    this.logger.log(`🔍 [AUTH SERVICE] Getting user by ID: ${userId}`);
    this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    try {
      // Extract raw encrypted access token from cookies
      const { accessToken: encryptedAccessToken } = this.extractRawTokensFromCookies(cookies);

      if (!encryptedAccessToken) {
        throw new UnauthorizedException('Missing access token');
      }

      // Get auth service URL from config
      const authServiceUrl = this.configService.get('AUTH_SERVICE_URL') || 'https://ng-auth-dev.dev1.ngnair.com';
      const url = `${authServiceUrl}/api/v1/users/${userId}`;

      this.logger.log(`🌐 [AUTH SERVICE] Making request to: ${url}`);
      this.logger.log(`🍪 [AUTH SERVICE] Forwarding cookie: access_token=${encryptedAccessToken.substring(0, 50)}...`);

      // Prepare headers for external service request
      const requestHeaders = {
        'Cookie': `access_token=${encryptedAccessToken}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Customer-Service/1.0',
      };

      // Log complete request details for debugging
      this.logger.log(`📋 [AUTH SERVICE] Complete request headers:`);
      this.logger.log(`   - Cookie: ${requestHeaders.Cookie.substring(0, 100)}...`);
      this.logger.log(`   - Content-Type: ${requestHeaders['Content-Type']}`);
      this.logger.log(`   - User-Agent: ${requestHeaders['User-Agent']}`);
      this.logger.log(`🔍 [AUTH SERVICE] Full encrypted token length: ${encryptedAccessToken.length} characters`);

      // Forward the encrypted access_token as a cookie to external service
      const response = await firstValueFrom(
        this.httpService.get(url, {
          headers: requestHeaders,
          timeout: 10000,
        })
      );

      this.logger.log(`✅ [AUTH SERVICE] User retrieved from external service`);
      this.logger.log(`📄 [AUTH SERVICE] Response data: ${JSON.stringify(response.data)}`);

      // Return the user data from the response
      return response.data;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Failed to get user by ID from external service: ${error.message}`);
      if (error.response?.status === 401) {
        throw new UnauthorizedException('Invalid or expired access token');
      }
      if (error.response?.status === 404) {
        throw new UnauthorizedException('User not found');
      }
      throw new UnauthorizedException('Failed to retrieve user information');
    }
  }

  /**
   * Get all users from external auth service
   */
  async getAllUsers(cookies: Record<string, string>): Promise<User[]> {
    this.logger.log('🔍 [AUTH SERVICE] Getting all users from external service');
    this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    try {
      // Extract raw encrypted access token from cookies
      const { accessToken: encryptedAccessToken } = this.extractRawTokensFromCookies(cookies);

      if (!encryptedAccessToken) {
        throw new UnauthorizedException('Missing access token');
      }

      // Get auth service URL from config
      const authServiceUrl = this.configService.get('AUTH_SERVICE_URL') || 'https://ng-auth-dev.dev1.ngnair.com';
      const url = `${authServiceUrl}/api/v1/users`;

      this.logger.log(`🌐 [AUTH SERVICE] Making request to: ${url}`);
      this.logger.log(`🍪 [AUTH SERVICE] Forwarding cookie: access_token=${encryptedAccessToken.substring(0, 50)}...`);

      // Prepare headers for external service request
      const requestHeaders = {
        'Cookie': `access_token=${encryptedAccessToken}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Customer-Service/1.0',
      };

      // Log complete request details for debugging
      this.logger.log(`📋 [AUTH SERVICE] Complete request headers:`);
      this.logger.log(`   - Cookie: ${requestHeaders.Cookie.substring(0, 100)}...`);
      this.logger.log(`   - Content-Type: ${requestHeaders['Content-Type']}`);
      this.logger.log(`   - User-Agent: ${requestHeaders['User-Agent']}`);
      this.logger.log(`🔍 [AUTH SERVICE] Full encrypted token length: ${encryptedAccessToken.length} characters`);

      // Forward the encrypted access_token as a cookie to external service
      const response = await firstValueFrom(
        this.httpService.get(url, {
          headers: requestHeaders,
          timeout: 10000,
        })
      );

      this.logger.log(`✅ [AUTH SERVICE] Users retrieved from external service`);
      this.logger.log(`📄 [AUTH SERVICE] Response data: ${JSON.stringify(response.data)}`);

      // Return the users array from the response
      const usersData = Array.isArray(response.data) ? response.data : [response.data];
      return usersData;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Failed to get all users from external service: ${error.message}`);
      if (error.response?.status === 401) {
        throw new UnauthorizedException('Invalid or expired access token');
      }
      throw new UnauthorizedException('Failed to retrieve users information');
    }
  }
}
