import { ApiProperty } from '@nestjs/swagger';

/**
 * User entity representing authenticated user information
 */
export class User {
  @ApiProperty({
    description: 'Unique user identifier',
    example: 'f7b98e6f-95af-4d54-9c53-312ada49ba6e'
  })
  id: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>'
  })
  email: string;

  @ApiProperty({
    description: 'Username (often same as email)',
    example: '<EMAIL>'
  })
  username: string;

  @ApiProperty({
    description: 'User first name',
    example: 'John',
    required: false
  })
  firstName?: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Doe',
    required: false
  })
  lastName?: string;

  @ApiProperty({
    description: 'User role',
    example: 'admin',
    enum: ['admin', 'user', 'moderator']
  })
  role: string;

  @ApiProperty({
    description: 'User permissions array',
    example: ['read', 'write', 'delete'],
    type: [String]
  })
  permissions: string[];

  @ApiProperty({
    description: 'Account creation timestamp',
    example: '2025-08-29T08:09:09.000Z'
  })
  createdAt: string;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2025-08-31T08:18:04.354Z'
  })
  updatedAt: string;

  // Additional fields from external auth service
  @ApiProperty({
    description: 'Associated accounts',
    required: false,
    type: [Object]
  })
  accounts?: any[];

  @ApiProperty({
    description: 'Associated partners',
    required: false,
    type: [Object]
  })
  partners?: any[];

  @ApiProperty({
    description: 'Active accounts flag',
    required: false
  })
  active_accounts?: boolean;

  @ApiProperty({
    description: 'Active partners flag',
    required: false
  })
  active_partners?: boolean;

  @ApiProperty({
    description: 'First name (alternative field)',
    required: false
  })
  first_name?: string;

  @ApiProperty({
    description: 'Last name (alternative field)',
    required: false
  })
  last_name?: string;
}

/**
 * JWT payload structure - matches Ruby JWT payload exactly
 * Based on the actual Ruby code structure:
 * {
 *   iss: ENV['JWT_ISSUER_URL'] || 'https://ngnair.com',
 *   sub: user.id,
 *   email: user.email,
 *   aud: [ENV['JWT_AUDIENCE'] || 'partner'],
 *   exp: Time.now.to_i + session_ttl,
 *   iat: Time.now.to_i,
 *   jti: SecureRandom.uuid,
 *   sid: "sess_#{SecureRandom.hex(8)}",
 *   azp: 'webapp',
 *   ent_set: ent_set,
 *   perm_v: perm_v,
 *   amr: ['pwd'],
 *   auth_time: Time.now.to_i
 * }
 */
export interface JWTPayload {
  // Standard JWT fields
  iss: string; // Issuer
  sub: string; // Subject (user ID)
  aud: string; // Audience
  exp: number; // Expiration time (Unix timestamp)
  iat: number; // Issued at (Unix timestamp)
  jti: string; // JWT ID (UUID)

  // NGNair-specific fields
  sid: string; // Session ID (e.g., "sess_35ee063223007077")
  azp: string; // Authorized party (e.g., "webapp")
  ent_set: string; // Entity set (hex string, e.g., "de2e18a49c8b")
  perm_v: number; // Permission version (e.g., 0)
  amr: string[]; // Authentication methods (e.g., ["pwd"])
  auth_time: number; // Authentication time (Unix timestamp)

  // Optional fields (may not be present in all JWTs)
  email?: string; // User email (optional)

  // Legacy fields for backward compatibility (deprecated)
  username?: string;
  firstName?: string;
  lastName?: string;
  first_name?: string;
  last_name?: string;
  role?: string;
  permissions?: string[];
  accounts?: any[];
  partners?: any[];
  active_accounts?: boolean;
  active_partners?: boolean;
}

/**
 * Decrypted tokens structure
 */
export interface DecryptedTokens {
  accessToken: string;
  refreshToken?: string;
}

/**
 * Cookie names configuration
 */
export interface CookieNames {
  accessToken: string;
  refreshToken: string;
}

/**
 * Authentication error response
 */
export class AuthErrorResponse {
  @ApiProperty({
    description: 'Error message',
    example: 'Authentication failed'
  })
  message: string;

  @ApiProperty({
    description: 'Error type',
    example: 'Unauthorized'
  })
  error: string;

  @ApiProperty({
    description: 'HTTP status code',
    example: 401
  })
  statusCode: number;
}

/**
 * Authentication success response for /auth/me
 */
export class AuthMeResponse extends User {}

/**
 * External service user response (for /auth/users endpoints)
 */
export class ExternalUserResponse {
  @ApiProperty({
    description: 'Response data from external auth service',
    type: 'object'
  })
  data: any;

  @ApiProperty({
    description: 'Response status',
    example: 'success'
  })
  status?: string;

  @ApiProperty({
    description: 'Response message',
    example: 'Users retrieved successfully'
  })
  message?: string;
}
