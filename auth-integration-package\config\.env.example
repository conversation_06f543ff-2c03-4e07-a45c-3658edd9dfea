# @ngnair/auth-integration-package Configuration
# Copy this file to .env and update the values

# =============================================================================
# REQUIRED CONFIGURATION
# =============================================================================

# AES-256 encryption key for decrypting Rails-compatible cookies
# Must be a 64-character hexadecimal string (32 bytes)
# Generate with: openssl rand -hex 32
ACCESS_TOKEN_ENCRYPTION_KEY=a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456

# External authentication service base URL
# Used for /auth/users and /auth/users/{id} endpoints
EXTERNAL_AUTH_SERVICE_URL=https://auth.example.com/api

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# JWKS URI for JWT verification
# If not provided, JWT verification will be skipped (development mode)
JWKS_URI=https://auth.example.com/.well-known/jwks.json

# Cookie names (defaults shown)
ACCESS_TOKEN_COOKIE_NAME=access_token
REFRESH_TOKEN_COOKIE_NAME=refresh_token

# Request timeout for external API calls (milliseconds)
AUTH_REQUEST_TIMEOUT=10000

# Enable debug logging (true/false)
AUTH_DEBUG_LOGGING=false

# JWT verification settings
JWT_ISSUER=https://auth.example.com
JWT_AUDIENCE=your-app-name
JWT_SKIP_VERIFICATION=false

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# For development, you might want to:
# 1. Set JWT_SKIP_VERIFICATION=true to skip JWT verification
# 2. Set AUTH_DEBUG_LOGGING=true to see detailed logs
# 3. Use a test encryption key

# Development encryption key (DO NOT USE IN PRODUCTION)
# ACCESS_TOKEN_ENCRYPTION_KEY=0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================

# For production, ensure:
# 1. Use a secure, randomly generated encryption key
# 2. Set proper JWKS_URI for JWT verification
# 3. Set AUTH_DEBUG_LOGGING=false
# 4. Set JWT_SKIP_VERIFICATION=false
# 5. Use HTTPS URLs for all external services
