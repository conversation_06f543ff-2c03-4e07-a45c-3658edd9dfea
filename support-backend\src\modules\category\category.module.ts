import { Modu<PERSON> } from "@nestjs/common";
import { PrismaModule } from "../../prisma/prisma.module";
import { CategoryController } from "./category.controller";
import { CategoryService } from "./category.service";
import { AuthModule } from "../auth/src/auth.module";
import { CategoryResolver } from './category.resolver';

@Module({
  imports: [PrismaModule, AuthModule],
  controllers: [CategoryController],
  providers: [CategoryService, CategoryResolver],
  exports: [CategoryService]
})
export class CategoryModule {}
