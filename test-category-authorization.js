#!/usr/bin/env node

const http = require('http');
const { URL } = require('url');

// Configuration
const BASE_URL = 'http://ng-support-local.dev.dev1.ngnair.com:3040';
const ACCESS_TOKEN = 'ZVZxY3hQRFlueDNZYzhLbU9nZGZnYVlQUllNenBHdGI5R2NLYnd2b2ZLSTNEdWZ0TThFL2M5ODBCNEgrd2daVDNHaU9SVCtnTFZZTUtNZlVETFpvTGU3Z0VDSURkamU4YU5nSXVJQTZhSTYxRU84d1VJZG5zZ0c5a2dWVWY3SVRkUWQySi83WEQzWFhaTkN5UEdDYW5JUFJQb015QWY4dENsaUl0NEtlalBtWG9SeFY0N04xSllJcnMxWnU0MktyZjFjakI5MHBKM3ZkUXNvRXY3d3M5OWUxcWFxdURFdEhhdTlkRjVSWGErWHBNalVUcjJlQ05JWEpPTEZjRG9NVVJ0Z01SUzZuU3k5YUtwKzNIVDBwZEFPdjJRZWVvbHIvdHUzbEZjcWdpTGY3dERqQ1FKWjZYU2tCVXVmNTN0WHF2c0RXdjJoOExQNnB3RTRybnFmT0VpQWhsWHpGRzFLTW10QXRHQTlyUk5yVHJMUENHd3NrOXB6Y04yWVZJbllQVHJZOGE1SEhHSSsrN0ZoK2hlS3pyOHBqRFRIaEZmVTdBMnFmQ2Z5L2lKVFEzdk9hankzTjNJdDdoNjRKaDVlSnRtcFlvdFRWMmIrY1RWZVRpZzV0UzMyRXpsenBUNEhFeER2RTdsV1pDeVJXL1JyNWh1ZkhwczhmbXJHMm9SYW9BUEVLTEtYRW4xV0wvODFtTm14Q0IyY01QUzNDS05PcFNqb1YxQ1ZhRmVUS2dkL0crOUk3c2ZXTWtRZjM4VHdvYjdPQ0cxUy9FV1luL0MreVlGVXFZMUF2dlM0K1JmWnZhenhYRjNpUW92bGpvQzFVMXlpblR5RFMvWHM3LzhTcG8rcWk3Z1R3bTVPM0FzNE1sY1EzeTlGQ1pRM01DRThDTmJVZ3NsanE2b2FnRXZMSkRtUno5c1cvUk9EMzZIcnJqdFo4MVNXSEZ5MVo5dGRTemp6TGlRL2dMQzlWSG1OSUJCZkFBbUtXR2tGNjVieDVXZFhOS2FPb3lZc010SWVOSVlGcmpQUjlnMlgzNnJ2d1FSMC9EbEdhaVM0d0NxbEZZMkd6QnFLbkZMbVhuclRrNFU4ZTdMcDJDbjFQZ3NmdWFNUEM2RDAzUkZOU0dyMzlTSjBQSnlTNUVPU1hjN2t4eFAxTGR6TFRRdnFrOEdxNW1zNi92WnF5ZnFMN05aTEUyWkhXQkRSL2N5UVp2dkVRR1RSZjJjd3NCRnlVVUxaMkc5WVgvcUtrN1dZZmFyWThMYTRhbmdpbG9XUjE4eFFYMENFaVRtV1lOc0taYWI3OEdsQ3BqbEtuSTBOeCtlSVM3UGFjRWtnMjUzdk5EeEcwbC9DTGUwb3UxcnVpZlBiZVU5R010WERORU5LYlJWZEJ5SXY1em84Y29IMDFKalV1OE1BOG1Fc0UwZk9UNDZqamdRbmowNFRzTGVKL0h6VmpBZ3BlSm9zUXk1dHdHVnBFdmQ2UmtRVXpGdkp5aUpSazByTDVaQT09LS1McFU1RTE2RUpKMDhWcDFkLS12Rzc0Tnd5akNndjFSY1FZRmlEMzRRPT0%3D';

console.log('🔒 CATEGORY AUTHORIZATION SPECIFIC TEST');
console.log('======================================');
console.log('Testing category endpoints to verify scope guard behavior');
console.log('');

// Helper function to make HTTP requests
function makeRequest(endpoint, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const url = new URL(endpoint, BASE_URL);
        const options = {
            hostname: url.hostname,
            port: url.port,
            path: url.pathname + url.search,
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Cookie': `access_token=${ACCESS_TOKEN}`,
                'User-Agent': 'Node.js-Test-Client/1.0'
            }
        };

        if (data) {
            const jsonData = JSON.stringify(data);
            options.headers['Content-Length'] = Buffer.byteLength(jsonData);
        }

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonBody = JSON.parse(body);
                    resolve({
                        success: res.statusCode >= 200 && res.statusCode < 300,
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        data: jsonBody,
                        headers: res.headers
                    });
                } catch (e) {
                    resolve({
                        success: false,
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        data: body,
                        headers: res.headers,
                        parseError: e.message
                    });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

async function testCategoryAuthorization() {
    try {
        console.log('⏳ Waiting 5 seconds for server to be ready...\n');
        
        setTimeout(async () => {
            console.log('🔍 TEST 1: GET /categories (Should succeed - @PartnerSupportAccess)');
            console.log('═'.repeat(70));
            
            const getCategoriesResult = await makeRequest('/categories');
            console.log(`Status: ${getCategoriesResult.status}`);
            console.log('Response:', JSON.stringify(getCategoriesResult.data, null, 2));
            
            if (getCategoriesResult.success) {
                console.log('✅ CORRECT: Category retrieval allowed for partner:support:user');
            } else {
                console.log('❌ UNEXPECTED: Category retrieval blocked');
            }
            
            console.log('\n🔍 TEST 2: POST /categories (Should fail - @GlobalAdminOnly)');
            console.log('═'.repeat(70));
            
            const categoryData = {
                name: 'Authorization Test Category',
                description: 'Testing authorization with fresh token - should be blocked'
            };
            
            const createCategoryResult = await makeRequest('/categories', 'POST', categoryData);
            console.log(`Status: ${createCategoryResult.status}`);
            console.log('Response:', JSON.stringify(createCategoryResult.data, null, 2));
            
            if (!createCategoryResult.success && (createCategoryResult.status === 401 || createCategoryResult.status === 403)) {
                console.log('✅ CORRECT: Category creation properly blocked');
                console.log('🎯 Authorization is working correctly!');
            } else if (createCategoryResult.success) {
                console.log('❌ AUTHORIZATION BYPASS DETECTED!');
                console.log('🚨 Category creation succeeded when it should be blocked');
                console.log('🔍 This indicates the @GlobalAdminOnly() decorator is not being enforced');
            } else {
                console.log('❓ UNEXPECTED ERROR');
                console.log('🔍 Different error than expected authorization failure');
            }
            
            console.log('\n📊 ANALYSIS');
            console.log('═'.repeat(50));
            
            if (getCategoriesResult.success && !createCategoryResult.success) {
                console.log('✅ SCOPE-BASED AUTHORIZATION: WORKING CORRECTLY');
                console.log('   • GET /categories: Allowed (✓)');
                console.log('   • POST /categories: Blocked (✓)');
                console.log('   • User scope: partner:support:user');
                console.log('   • Authorization guards: Functioning properly');
            } else if (getCategoriesResult.success && createCategoryResult.success) {
                console.log('❌ SCOPE-BASED AUTHORIZATION: BYPASS DETECTED');
                console.log('   • GET /categories: Allowed (✓)');
                console.log('   • POST /categories: Allowed (❌ Should be blocked)');
                console.log('   • User scope: partner:support:user');
                console.log('   • Issue: @GlobalAdminOnly() not enforced');
            } else if (!getCategoriesResult.success && !createCategoryResult.success) {
                console.log('❌ SCOPE-BASED AUTHORIZATION: OVERLY RESTRICTIVE');
                console.log('   • GET /categories: Blocked (❌ Should be allowed)');
                console.log('   • POST /categories: Blocked (✓)');
                console.log('   • User scope: partner:support:user');
                console.log('   • Issue: @PartnerSupportAccess() not working');
            } else {
                console.log('❓ SCOPE-BASED AUTHORIZATION: INCONSISTENT BEHAVIOR');
                console.log('   • Unexpected combination of results');
                console.log('   • Requires further investigation');
            }
            
            console.log('\n🔧 NEXT STEPS');
            console.log('═'.repeat(50));
            
            if (createCategoryResult.success) {
                console.log('1. Check server logs for scope guard activity during POST /categories');
                console.log('2. Verify @GlobalAdminOnly() decorator is properly applied');
                console.log('3. Ensure ScopeGuard is running for category creation');
                console.log('4. Check if guards are being bypassed somehow');
            } else {
                console.log('1. Authorization is working correctly');
                console.log('2. Both issues should be resolved once user data is fixed');
                console.log('3. Focus on fixing user data service endpoint');
            }
            
            console.log('\n🏁 Test completed!');
            
        }, 5000);
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run the test
testCategoryAuthorization().catch(console.error);
