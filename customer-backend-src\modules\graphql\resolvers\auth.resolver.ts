import { Resolver, Query, Context } from '@nestjs/graphql';
import { Logger } from '@nestjs/common';
import { AuthSharedService } from '../../auth-shared/auth.service';
import { JWTPayload, User } from '../types/auth.types';

@Resolver()
export class AuthResolver {
  private readonly logger = new Logger(AuthResolver.name);

  constructor(private readonly authService: AuthSharedService) {}

  @Query(() => JWTPayload, { name: 'me', description: 'Get current user JWT payload from authentication service' })
  async getCurrentUser(@Context() context: any): Promise<any> {
    this.logger.log('🔍 [AUTH RESOLVER] me query called');

    const request = context.req;

    if (!request) {
      this.logger.error('❌ [AUTH RESOLVER] Request context not available');
      throw new Error('Request context not available');
    }

    // Extract cookies from request
    const cookies = request.cookies || {};
    this.logger.log(`🍪 [AUTH RESOLVER] Cookies available: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    if (!cookies.access_token) {
      this.logger.warn('❌ [AUTH RESOLVER] No access token found in cookies');
      throw new Error('Authentication required - no access token');
    }

    try {
      this.logger.log('🔐 [AUTH RESOLVER] Authenticating user and extracting JWT payload...');

      const jwtPayload = await this.authService.authenticateUserFromCookies(cookies);

      this.logger.log(`✅ [AUTH RESOLVER] JWT payload retrieved successfully for user: ${jwtPayload.sub}`);
      this.logger.log(`📋 [AUTH RESOLVER] JWT payload fields: ${Object.keys(jwtPayload).join(', ')}`);
      
      return jwtPayload;
    } catch (error) {
      this.logger.error(`❌ [AUTH RESOLVER] Error getting current user: ${error.message}`);
      throw error;
    }
  }

  @Query(() => [User], { name: 'users', description: 'Get all users from external auth service' })
  async getAllUsers(@Context() context: any): Promise<User[]> {
    this.logger.log('🔍 [AUTH RESOLVER] users query called');

    const request = context.req;

    if (!request) {
      this.logger.error('❌ [AUTH RESOLVER] Request context not available');
      throw new Error('Request context not available');
    }

    // Extract cookies from request
    const cookies = request.cookies || {};
    this.logger.log(`🍪 [AUTH RESOLVER] Cookies available: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    if (!cookies.access_token) {
      this.logger.warn('❌ [AUTH RESOLVER] No access token found in cookies');
      throw new Error('Authentication required - no access token');
    }

    try {
      this.logger.log('🔐 [AUTH RESOLVER] Fetching users from external auth service...');

      const users = await this.authService.getAllUsers(cookies);

      this.logger.log(`✅ [AUTH RESOLVER] Users retrieved successfully: ${users.length} users`);

      return users;
    } catch (error) {
      this.logger.error(`❌ [AUTH RESOLVER] Error getting all users: ${error.message}`);
      throw error;
    }
  }

}
