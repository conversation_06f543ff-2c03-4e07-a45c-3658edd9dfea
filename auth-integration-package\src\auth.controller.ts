import { <PERSON>, <PERSON>, Lo<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags, ApiParam } from '@nestjs/swagger';
import { Request } from 'express';
import { AuthService } from './auth.service';
import { AuthGuard } from './guards/auth.guard';
import { User } from './types/auth.types';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(private readonly authService: AuthService) {}

  @ApiOperation({
    summary: 'Get current user information',
    description: 'Authenticates user using encrypted cookies, decrypts access token, verifies JWT locally, and returns ONLY actual JWT payload fields (no computed/hardcoded fields)'
  })
  @ApiResponse({
    status: 200,
    description: 'JWT payload extracted successfully - returns only actual fields from the JWT token',
    schema: {
      type: 'object',
      properties: {
        iss: { type: 'string', description: 'JWT issuer', example: 'https://ng-auth-dev.dev1.ngnair.com' },
        sub: { type: 'string', description: 'User ID (subject)', example: 'f7b98e6f-95af-4d54-9c53-312ada49ba6e' },
        aud: { type: 'string', description: 'JWT audience', example: 'ngnair' },
        exp: { type: 'number', description: 'Expiration time (Unix timestamp)', example: 1756774160 },
        iat: { type: 'number', description: 'Issued at time (Unix timestamp)', example: 1756687760 },
        jti: { type: 'string', description: 'JWT ID (UUID)', example: '1af073f4-71e4-4aca-99b9-f41f92770a0a' },
        sid: { type: 'string', description: 'Session ID', example: 'sess_35ee063223007077' },
        azp: { type: 'string', description: 'Authorized party', example: 'webapp' },
        ent_set: { type: 'string', description: 'Entity set (hex string)', example: 'de2e18a49c8b' },
        perm_v: { type: 'number', description: 'Permission version', example: 0 },
        amr: { type: 'array', items: { type: 'string' }, description: 'Authentication methods', example: ['pwd'] },
        auth_time: { type: 'number', description: 'Authentication time (Unix timestamp)', example: 1756687760 },
        email: { type: 'string', description: 'User email (optional - may not be present in all JWTs)', example: '<EMAIL>' }
      },
      required: ['iss', 'sub', 'aud', 'exp', 'iat', 'jti', 'sid', 'azp', 'ent_set', 'perm_v', 'amr', 'auth_time']
    }
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication failed - invalid or missing access token',
  })
  @Get('me')
  async getCurrentUser(@Req() request: Request): Promise<any> {
    try {
      this.logger.log('🔑 [AUTH CONTROLLER] Getting current user information');

      // Extract cookies from request
      const cookies = request.cookies || {};
      this.logger.log(`🍪 [AUTH CONTROLLER] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

      // Check if access token is present
      const accessTokenPresent = !!cookies.access_token;
      this.logger.log(`🔑 [AUTH CONTROLLER] Access token present: ${accessTokenPresent}`);

      if (!accessTokenPresent) {
        this.logger.error('❌ [AUTH CONTROLLER] No access token found in cookies');
        throw new Error('Missing access token');
      }

      // Authenticate user using the new method that properly handles external auth service
      const jwtPayload = await this.authService.authenticateUserFromCookies(cookies);

      this.logger.log(`✅ [AUTH CONTROLLER] JWT payload extracted successfully for user: ${jwtPayload.sub}`);
      this.logger.log(`📋 [AUTH CONTROLLER] JWT fields returned: ${JSON.stringify(Object.keys(jwtPayload))}`);

      return jwtPayload;
    } catch (error) {
      this.logger.error(`❌ [AUTH CONTROLLER] Authentication failed: ${error.message}`);
      throw error;
    }
  }

  @ApiOperation({
    summary: 'Get all users',
    description: 'Retrieves all users from external authentication service using forwarded encrypted cookies'
  })
  @ApiResponse({
    status: 200,
    description: 'Users retrieved successfully from external service',
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication failed or external service unavailable',
  })
  @UseGuards(AuthGuard)
  @Get('users')
  async getAllUsers(@Req() request: Request): Promise<any> {
    try {
      this.logger.log('🔍 [AUTH CONTROLLER] Getting all users from external service');

      // Extract cookies from request
      const cookies = request.cookies || {};
      this.logger.log(`🍪 [AUTH CONTROLLER] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

      // Forward request to external auth service
      const users = await this.authService.getAllUsers(cookies);

      this.logger.log(`✅ [AUTH CONTROLLER] Users retrieved successfully: ${Array.isArray(users) ? users.length : 'unknown'} users`);
      return users;
    } catch (error) {
      this.logger.error(`❌ [AUTH CONTROLLER] Get all users failed: ${error.message}`);
      throw error;
    }
  }

  @ApiOperation({
    summary: 'Get user by ID',
    description: 'Retrieves a specific user by ID from external authentication service using forwarded encrypted cookies'
  })
  @ApiParam({
    name: 'id',
    description: 'User ID',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: 'User retrieved successfully from external service',
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication failed or external service unavailable',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  @UseGuards(AuthGuard)
  @Get('users/:id')
  async getUserById(@Param('id') id: string, @Req() request: Request): Promise<any> {
    try {
      this.logger.log(`🔍 [AUTH CONTROLLER] Getting user by ID: ${id}`);

      // Extract cookies from request
      const cookies = request.cookies || {};
      this.logger.log(`🍪 [AUTH CONTROLLER] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

      // Forward request to external auth service
      const user = await this.authService.getUserById(id, cookies);

      this.logger.log(`✅ [AUTH CONTROLLER] User ${id} retrieved successfully`);
      return user;
    } catch (error) {
      this.logger.error(`❌ [AUTH CONTROLLER] Get user ${id} failed: ${error.message}`);
      throw error;
    }
  }
}
