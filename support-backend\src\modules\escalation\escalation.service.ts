import { Injectable, Logger } from "@nestjs/common";
import { Interval } from "@nestjs/schedule";
import { Ticket, Category, TicketStatus, TicketPriority, Comment, /* SupportFile, */ CategoryType } from "@prisma/client";
import { PrismaService } from "../../prisma/prisma.service";
import { TicketPayload, ticketInclude } from "../ticket/types/ticket.types";
import { NotificationService } from "../notification/notification.service";

// Full category type including required fields
type FullCategory = Category & {
  timeoutMinutes: number;
  escalateTo: string | null;
  type: CategoryType;
  autoAssignTo: string[];
};

// Interface that matches the full ticket payload with all relations
interface TicketWithRelations extends Ticket {
  category: FullCategory;
  comments: Comment[];
  // files: SupportFile[];
}

const ESCALATION_CHECK_INTERVAL = 5 * 60 * 1000; // Check every 5 minutes

@Injectable()
export class EscalationService {
  private readonly logger = new Logger(EscalationService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly notificationService: NotificationService,
  ) {}

  @Interval(ESCALATION_CHECK_INTERVAL)
  async checkForEscalations(): Promise<void> {
    this.logger.debug("Checking for tickets that need escalation...");

    try {
      // Find tickets that are:
      // 1. Not closed/resolved
      // 2. Have been open longer than their category's timeout
      const ticketsToEscalate = await this.prisma.ticket.findMany({
        where: {
          status: {
            notIn: [TicketStatus.CLOSED, TicketStatus.RESOLVED]
          },
          category: {
            escalateTo: {
              not: null
            }
          }
        },
        include: ticketInclude // Include all necessary relations
      });

      for (const ticket of ticketsToEscalate) {
        // We know timeoutMinutes is required by schema
        const timeoutMinutes = (ticket.category as FullCategory).timeoutMinutes;
        const timeoutMs = timeoutMinutes * 60 * 1000;
        const timeSinceLastUpdate = Date.now() - ticket.updatedAt.getTime();

        if (timeSinceLastUpdate >= timeoutMs) {
          // Ticket has exceeded its timeout and should be escalated
          await this.escalateTicket(ticket as unknown as TicketWithRelations);
        }
      }
    } catch (error) {
      this.logger.error("Error checking for escalations:", error);
    }
  }

  private async escalateTicket(ticket: TicketWithRelations): Promise<void> {
    this.logger.debug(`Escalating ticket ${ticket.id}`);

    try {
      const { category } = ticket;
      
      if (!category.escalateTo) {
        this.logger.warn(`No escalation target for ticket ${ticket.id}`);
        return;
      }      // Update ticket assignees to include escalation target
      const currentAssignees = ticket.assignedTo ?? [];
      const updatedTicket = await this.prisma.ticket.update({
        where: { id: ticket.id },
        data: {
          assignedTo: category.escalateTo ? [...currentAssignees, category.escalateTo] : currentAssignees,
          priority: ticket.priority === TicketPriority.URGENT ? TicketPriority.URGENT : 
            ticket.priority === TicketPriority.HIGH ? TicketPriority.URGENT :
            ticket.priority === TicketPriority.MEDIUM ? TicketPriority.HIGH : TicketPriority.MEDIUM
        },
        include: ticketInclude
      });

      // Send escalation notification with fully loaded ticket data
      await this.notificationService.sendEscalationNotification(
        updatedTicket as unknown as TicketPayload, 
        category as Category
      );

      this.logger.debug(`Successfully escalated ticket ${ticket.id}`);
    } catch (error) {
      this.logger.error(`Error escalating ticket ${ticket.id}:`, error);
    }
  }
}
