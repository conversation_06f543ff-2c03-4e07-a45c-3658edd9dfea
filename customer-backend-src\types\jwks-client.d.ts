declare module 'jwks-client' {
  interface JwksClientOptions {
    jwksUri: string;
    requestHeaders?: Record<string, string>;
    timeout?: number;
    cache?: boolean;
    rateLimit?: boolean;
    jwksRequestsPerMinute?: number;
    jwksRequestsPerMinute?: number;
  }

  interface SigningKey {
    getPublicKey(): string;
    getPublicKey(callback: (err: Error | null, publicKey: string) => void): void;
  }

  interface JwksClient {
    getSigningKey(kid: string): Promise<SigningKey>;
    getSigningKey(kid: string, callback: (err: Error | null, key: SigningKey) => void): void;
  }

  function jwksClient(options: JwksClientOptions): JwksClient;
  export = jwksClient;
}
