import { Controller, Get, Query, Request, UseGuards, Optional } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiQuery, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { PartnerService } from '../services/partner.service';
import { GetUserScopesParams, GetPartnerInfoParams } from '../types/partner.types';

@ApiTags('Partner Integration')
@Controller('partner-integration')
export class PartnerController {
  constructor(private readonly partnerService: PartnerService) {}

  @Get('partners')
  @ApiOperation({ summary: 'Get all partners' })
  @ApiResponse({ status: 200, description: 'Partners retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'External API error' })
  async getPartners(@Request() req: any) {
    try {
      // Extract access token from cookies or headers
      const accessToken = this.extractAccessToken(req);
      
      if (!accessToken) {
        return {
          success: false,
          error: 'No access token found'
        };
      }

      return await this.partnerService.getPartners(accessToken);

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  @Get('partner-info')
  @ApiOperation({ summary: 'Get partner information' })
  @ApiQuery({ name: 'partner_id', required: true, description: 'Partner ID (public_uid)' })
  @ApiResponse({ status: 200, description: 'Partner info retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'External API error' })
  async getPartnerInfo(
    @Query('partner_id') partnerId: string,
    @Request() req: any
  ) {
    try {
      const accessToken = this.extractAccessToken(req);
      
      if (!accessToken) {
        return {
          success: false,
          error: 'No access token found'
        };
      }

      const params: GetPartnerInfoParams = { partnerId };
      return await this.partnerService.getPartnerInfo(params, accessToken);

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  @Get('user-scopes')
  @ApiOperation({ summary: 'Get user scopes for partner integration' })
  @ApiQuery({ name: 'user_id', required: true, description: 'User ID (sub from JWT)' })
  @ApiQuery({ name: 'partner_id', required: true, description: 'Partner ID (public_uid)' })
  @ApiQuery({ name: 'tenant_type', required: false, description: 'Tenant type (partner or account)', enum: ['partner', 'account'] })
  @ApiResponse({ status: 200, description: 'User scopes retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'External API error' })
  async getUserScopes(
    @Query('user_id') userId: string,
    @Query('partner_id') partnerId: string,
    @Query('tenant_type') tenantType: string = 'partner',
    @Request() req: any
  ) {
    try {
      const accessToken = this.extractAccessToken(req);
      
      if (!accessToken) {
        return {
          success: false,
          error: 'No access token found'
        };
      }

      const params: GetUserScopesParams = {
        userId,
        partnerId,
        tenantType: tenantType as 'partner' | 'account'
      };

      return await this.partnerService.getUserScopes(params, accessToken);

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  @Get('scope-context')
  @ApiOperation({ summary: 'Resolve complete scope context for a user' })
  @ApiQuery({ name: 'user_id', required: true, description: 'User ID (sub from JWT)' })
  @ApiQuery({ name: 'partner_id', required: true, description: 'Partner ID (public_uid)' })
  @ApiQuery({ name: 'tenant_type', required: false, description: 'Tenant type (partner or account)', enum: ['partner', 'account'] })
  @ApiResponse({ status: 200, description: 'Scope context resolved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'External API error' })
  async resolveScopeContext(
    @Query('user_id') userId: string,
    @Query('partner_id') partnerId: string,
    @Query('tenant_type') tenantType: string = 'partner',
    @Request() req: any
  ) {
    try {
      const accessToken = this.extractAccessToken(req);
      
      if (!accessToken) {
        return {
          success: false,
          error: 'No access token found'
        };
      }

      const params: GetUserScopesParams = {
        userId,
        partnerId,
        tenantType: tenantType as 'partner' | 'account'
      };

      return await this.partnerService.resolveScopeContext(params, accessToken);

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Extract access token from request (cookies or headers)
   */
  private extractAccessToken(req: any): string | null {
    // Try cookies first
    const cookies = req.cookies || {};
    if (cookies.access_token) {
      return cookies.access_token;
    }

    // Try Authorization header
    const authHeader = req.headers?.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    return null;
  }
}
