import { Controller, Get, Post, Body, Param, Delete, UseGuards, Request } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { ApiGuard } from "../auth/src/guards";
import { AuthenticatedRequest } from "../auth/src/types";
import { CreateCommentDto } from "./dto/create-comment.dto";
import { CommentService } from "./comment.service";
import { CommentPayload } from "./types/comment.types";

@ApiTags("Comments")
@Controller("comments")
@UseGuards(ApiGuard)
@ApiBearerAuth()
export class CommentController {
  constructor(private readonly commentService: CommentService) {}

  @Post()
  @ApiOperation({ summary: "Create a new comment" })
  async create(
    @Body() createCommentDto: CreateCommentDto,
    @Request() req: AuthenticatedRequest
  ): Promise<CommentPayload> {
    return this.commentService.create({
      message: createCommentDto.message,
      authorId: req.user.id,
      ticket: {
        connect: { id: createCommentDto.ticketId }
      }
    });
  }

  @Get("ticket/:ticketId")
  @ApiOperation({ summary: "Get all comments for a ticket" })
  async findByTicketId(@Param("ticketId") ticketId: string): Promise<CommentPayload[]> {
    return this.commentService.findByTicketId(ticketId);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a comment" })
  @ApiResponse({ status: 200, description: "Comment deleted successfully" })
  @ApiResponse({ status: 404, description: "Comment not found" })
  async remove(@Param("id") id: string): Promise<CommentPayload> {
    return this.commentService.delete({ id });
  }
}
