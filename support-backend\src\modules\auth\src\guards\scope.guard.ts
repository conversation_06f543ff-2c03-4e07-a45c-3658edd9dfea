import { Injectable, CanActivate, ExecutionContext, ForbiddenException, Logger, SetMetadata } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import 'reflect-metadata';
import { ScopeResolutionService } from '../scope-resolution.service';
import { 
  SupportScope, 
  ScopeContext, 
  getScopePermissions,
  ScopePermissions 
} from '../types/scope.types';

export const REQUIRED_SCOPES_KEY = 'requiredScopes';
export const REQUIRED_PERMISSIONS_KEY = 'requiredPermissions';

/**
 * Guard that enforces scope-based authorization
 */
@Injectable()
export class ScopeGuard implements CanActivate {
  private readonly logger = new Logger(ScopeGuard.name);

  constructor(
    private reflector: Reflector,
    private scopeResolutionService: ScopeResolutionService
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      // Get required scopes from decorator
      const requiredScopes = this.reflector.getAllAndOverride<SupportScope[]>(
        REQUIRED_SCOPES_KEY,
        [context.getHandler(), context.getClass()]
      );

      // Get required permissions from decorator
      const requiredPermissions = this.reflector.getAllAndOverride<(keyof ScopePermissions)[]>(
        REQUIRED_PERMISSIONS_KEY,
        [context.getHandler(), context.getClass()]
      );

      // Debug logging
      this.logger.log(`🔍 [SCOPE GUARD] Required scopes: ${JSON.stringify(requiredScopes)}`);
      this.logger.log(`🔍 [SCOPE GUARD] Required permissions: ${JSON.stringify(requiredPermissions)}`);

      // Additional debug logging
      const handlerName = context.getHandler().name;
      const className = context.getClass().name;
      this.logger.log(`🔍 [SCOPE GUARD] Handler: ${className}.${handlerName}`);

      // Check metadata directly
      const directScopes = Reflect.getMetadata(REQUIRED_SCOPES_KEY, context.getHandler());
      const directClassScopes = Reflect.getMetadata(REQUIRED_SCOPES_KEY, context.getClass());
      this.logger.log(`🔍 [SCOPE GUARD] Direct handler metadata: ${JSON.stringify(directScopes)}`);
      this.logger.log(`🔍 [SCOPE GUARD] Direct class metadata: ${JSON.stringify(directClassScopes)}`);

      // If no scopes or permissions required, allow access
      if (!requiredScopes && !requiredPermissions) {
        this.logger.warn(`⚠️ [SCOPE GUARD] No scopes or permissions required - allowing access`);
        return true;
      }

      const request = context.switchToHttp().getRequest();
      const user = request.user;

      if (!user || !user.sub) {
        this.logger.warn(`❌ [SCOPE GUARD] No user or sub found in request`);
        throw new ForbiddenException('User authentication required');
      }

      // Extract access token from cookies
      const accessToken = this.scopeResolutionService.extractAccessToken(request.cookies);

      // Resolve user scopes
      const scopeContext = await this.scopeResolutionService.resolveUserScopes(user.sub, accessToken);
      
      // Add scope context to request for use in controllers/services
      request.scopeContext = scopeContext;

      this.logger.log(`🔍 [SCOPE GUARD] User ${user.sub} has scope: ${scopeContext.scope}`);

      // Global authorization check - ensure user has valid support scope
      const validSupportScopes = [
        SupportScope.PARTNER_SUPPORT_USER,
        SupportScope.PARTNER_SUPPORT_ADMIN,
        SupportScope.GLOBAL_SUPPORT_ADMIN
      ];

      if (!validSupportScopes.includes(scopeContext.scope)) {
        this.logger.warn(`❌ [SCOPE GUARD] User ${user.sub} has invalid scope: ${scopeContext.scope}. Valid scopes: ${validSupportScopes.join(', ')}`);
        throw new ForbiddenException('Unauthorized: Invalid scope for support system access');
      }

      // Check if user has required scopes
      if (requiredScopes && requiredScopes.length > 0) {
        const hasRequiredScope = requiredScopes.includes(scopeContext.scope);
        this.logger.log(`🔍 [SCOPE GUARD] Scope validation - User: ${user.sub}, Has: ${scopeContext.scope}, Required: [${requiredScopes.join(', ')}], Match: ${hasRequiredScope}`);

        if (!hasRequiredScope) {
          this.logger.warn(`❌ [SCOPE GUARD] User ${user.sub} lacks required scope. Has: ${scopeContext.scope}, Required: ${requiredScopes.join(', ')}`);
          throw new ForbiddenException(`Insufficient scope permissions. Required: ${requiredScopes.join(' OR ')}, but user has: ${scopeContext.scope}`);
        }
      }

      // Check if user has required permissions
      if (requiredPermissions && requiredPermissions.length > 0) {
        const userPermissions = getScopePermissions(scopeContext.scope);
        const hasAllPermissions = requiredPermissions.every(permission => userPermissions[permission]);
        
        if (!hasAllPermissions) {
          const missingPermissions = requiredPermissions.filter(permission => !userPermissions[permission]);
          this.logger.warn(`❌ [SCOPE GUARD] User ${user.sub} lacks required permissions: ${missingPermissions.join(', ')}`);
          throw new ForbiddenException('Insufficient permissions');
        }
      }

      this.logger.log(`✅ [SCOPE GUARD] Access granted for user ${user.sub} with scope ${scopeContext.scope}`);
      return true;

    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      
      this.logger.error(`❌ [SCOPE GUARD] Error during scope validation:`, error.message);
      throw new ForbiddenException('Authorization failed');
    }
  }
}

/**
 * Decorator to require specific scopes
 */
export const RequireScopes = (...scopes: SupportScope[]) => {
  console.log('🔧 [DECORATOR] RequireScopes called with scopes:', scopes);
  console.log('🔧 [DECORATOR] REQUIRED_SCOPES_KEY:', REQUIRED_SCOPES_KEY);
  console.log('🔧 [DECORATOR] Using SetMetadata instead of Reflect.metadata');
  return SetMetadata(REQUIRED_SCOPES_KEY, scopes);
};

/**
 * Decorator to require specific permissions
 */
export const RequirePermissions = (...permissions: (keyof ScopePermissions)[]) => {
  return Reflect.metadata(REQUIRED_PERMISSIONS_KEY, permissions);
};

/**
 * Decorator for global admin only access
 */
export const GlobalAdminOnly = () => {
  console.log('🔧 [DECORATOR] GlobalAdminOnly called, setting scope:', SupportScope.GLOBAL_SUPPORT_ADMIN);
  return RequireScopes(SupportScope.GLOBAL_SUPPORT_ADMIN);
};

/**
 * Decorator for admin level access (global, partner, or account admin)
 */
export const AdminOnly = () => RequireScopes(
  SupportScope.GLOBAL_SUPPORT_ADMIN,
  SupportScope.PARTNER_SUPPORT_ADMIN,
  SupportScope.ACCOUNT_SUPPORT_ADMIN
);

/**
 * Decorator for any support access
 */
export const SupportAccess = () => RequireScopes(
  SupportScope.GLOBAL_SUPPORT_ADMIN,
  SupportScope.PARTNER_SUPPORT_ADMIN,
  SupportScope.PARTNER_SUPPORT_USER,
  SupportScope.ACCOUNT_SUPPORT_ADMIN,
  SupportScope.ACCOUNT_SUPPORT_USER
);

/**
 * Decorator for partner support access (admin or user)
 */
export const PartnerSupportAccess = () => RequireScopes(
  SupportScope.PARTNER_SUPPORT_ADMIN,
  SupportScope.PARTNER_SUPPORT_USER
);

/**
 * Decorator for category management access
 */
export const CategoryManagement = () => RequirePermissions('canManageCategories');

/**
 * Decorator for soft delete viewing access
 */
export const ViewSoftDeleted = () => RequirePermissions('canViewSoftDeleted');
