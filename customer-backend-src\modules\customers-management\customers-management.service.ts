import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { Prisma } from '@prisma/client';
import { CustomerWithRelations, CustomersQueryService } from '../customers-query/customers-query.service';

// Re-export for convenience
export { CustomerWithRelations } from '../customers-query/customers-query.service';

@Injectable()
export class CustomersManagementService {
  private readonly logger = new Logger(CustomersManagementService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly queryService: CustomersQueryService,
  ) {}

  async create(data: Prisma.CustomerCreateInput): Promise<CustomerWithRelations> {
    this.logger.log(`Creating customer: ${data.email}`);

    // Check if customer with email already exists
    const existingCustomer = await this.prisma.customer.findUnique({
      where: { email: data.email },
    });

    if (existingCustomer) {
      throw new BadRequestException(`Customer with email ${data.email} already exists`);
    }

    const customer = await this.prisma.customer.create({
      data,
      include: {
        addresses: true,
        contacts: true,
        preferences: true,
      },
    });

    this.logger.log(`Customer created successfully: ${customer.id}`);
    return customer;
  }

  async update(
    id: string,
    data: Prisma.CustomerUpdateInput,
  ): Promise<CustomerWithRelations> {
    this.logger.log(`Updating customer: ${id}`);

    const existingCustomer = await this.queryService.findById(id);
    if (!existingCustomer) {
      throw new NotFoundException(`Customer not found: ${id}`);
    }

    const customer = await this.prisma.customer.update({
      where: { id },
      data,
      include: {
        addresses: true,
        contacts: true,
        preferences: true,
      },
    });

    this.logger.log(`Customer updated successfully: ${id}`);
    return customer;
  }

  async delete(id: string): Promise<CustomerWithRelations> {
    this.logger.log(`Soft deleting customer: ${id}`);

    const existingCustomer = await this.queryService.findById(id);
    if (!existingCustomer) {
      throw new NotFoundException(`Customer not found: ${id}`);
    }

    const customer = await this.prisma.customer.update({
      where: { id },
      data: { deletedAt: new Date() },
      include: {
        addresses: true,
        contacts: true,
        preferences: true,
      },
    });

    this.logger.log(`Customer soft deleted successfully: ${id}`);
    return customer;
  }

  async updateStatus(
    id: string,
    status: string,
  ): Promise<CustomerWithRelations> {
    this.logger.log(`Updating customer status: ${id} to ${status}`);

    const existingCustomer = await this.queryService.findById(id);
    if (!existingCustomer) {
      throw new NotFoundException(`Customer not found: ${id}`);
    }

    const customer = await this.prisma.customer.update({
      where: { id },
      data: { status: status as any },
      include: {
        addresses: true,
        contacts: true,
        preferences: true,
      },
    });

    this.logger.log(`Customer status updated successfully: ${id}`);
    return customer;
  }

  async verifyEmail(id: string): Promise<CustomerWithRelations> {
    this.logger.log(`Verifying email for customer: ${id}`);

    const existingCustomer = await this.queryService.findById(id);
    if (!existingCustomer) {
      throw new NotFoundException(`Customer not found: ${id}`);
    }

    const customer = await this.prisma.customer.update({
      where: { id },
      data: {
        isEmailVerified: true,
      },
      include: {
        addresses: true,
        contacts: true,
        preferences: true,
      },
    });

    this.logger.log(`Email verified successfully for customer: ${id}`);
    return customer;
  }

  async verifyPhone(id: string): Promise<CustomerWithRelations> {
    this.logger.log(`Verifying phone for customer: ${id}`);

    const existingCustomer = await this.queryService.findById(id);
    if (!existingCustomer) {
      throw new NotFoundException(`Customer not found: ${id}`);
    }

    const customer = await this.prisma.customer.update({
      where: { id },
      data: {
        isPhoneVerified: true,
      },
      include: {
        addresses: true,
        contacts: true,
        preferences: true,
      },
    });

    this.logger.log(`Phone verified successfully for customer: ${id}`);
    return customer;
  }

  async bulkUpdate(
    ids: string[],
    data: Prisma.CustomerUpdateInput,
  ): Promise<{ count: number }> {
    this.logger.log(`Bulk updating ${ids.length} customers`);

    const result = await this.prisma.customer.updateMany({
      where: {
        id: { in: ids },
        deletedAt: null,
      },
      data,
    });

    this.logger.log(`Bulk update completed: ${result.count} customers updated`);
    return { count: result.count };
  }

  async bulkDelete(ids: string[]): Promise<{ count: number }> {
    this.logger.log(`Bulk soft deleting ${ids.length} customers`);

    const result = await this.prisma.customer.updateMany({
      where: {
        id: { in: ids },
        deletedAt: null,
      },
      data: { deletedAt: new Date() },
    });

    this.logger.log(`Bulk delete completed: ${result.count} customers deleted`);
    return { count: result.count };
  }

  async addTag(id: string, tag: string): Promise<CustomerWithRelations> {
    this.logger.log(`Adding tag '${tag}' to customer: ${id}`);

    const existingCustomer = await this.queryService.findById(id);
    if (!existingCustomer) {
      throw new NotFoundException(`Customer not found: ${id}`);
    }
    const currentTags = existingCustomer.tags || [];

    if (currentTags.includes(tag)) {
      throw new BadRequestException(`Customer already has tag: ${tag}`);
    }

    const updatedTags = [...currentTags, tag];

    return this.update(id, { tags: updatedTags });
  }

  async removeTag(id: string, tag: string): Promise<CustomerWithRelations> {
    this.logger.log(`Removing tag '${tag}' from customer: ${id}`);

    const existingCustomer = await this.queryService.findById(id);
    if (!existingCustomer) {
      throw new NotFoundException(`Customer not found: ${id}`);
    }
    const currentTags = existingCustomer.tags || [];

    if (!currentTags.includes(tag)) {
      throw new BadRequestException(`Customer does not have tag: ${tag}`);
    }

    const updatedTags = currentTags.filter(t => t !== tag);

    return this.update(id, { tags: updatedTags });
  }

  async updateNotes(id: string, notes: string): Promise<CustomerWithRelations> {
    this.logger.log(`Updating notes for customer: ${id}`);

    return this.update(id, { notes });
  }
}
