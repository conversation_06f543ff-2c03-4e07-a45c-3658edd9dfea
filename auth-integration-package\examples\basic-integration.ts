/**
 * Basic Integration Example
 * 
 * This example shows how to integrate the auth package into a basic NestJS application
 */

import { Module } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { Controller, Get, UseGuards, Req } from '@nestjs/common';
import * as cookieParser from 'cookie-parser';
import { 
  AuthModule, 
  AuthGuard, 
  OptionalAuthGuard,
  User 
} from '@ngnair/auth-integration-package';

// =============================================================================
// STEP 1: Create your application controller
// =============================================================================

@Controller('api')
export class AppController {
  
  // Public endpoint - no authentication required
  @Get('public')
  getPublicData() {
    return { 
      message: 'This is public data',
      timestamp: new Date().toISOString()
    };
  }

  // Protected endpoint - authentication required
  @UseGuards(AuthGuard)
  @Get('protected')
  getProtectedData(@Req() request: any) {
    const user: User = request.user; // User is automatically attached by AuthGuard
    return {
      message: 'This is protected data',
      user: {
        id: user.id,
        email: user.email,
        role: user.role
      },
      timestamp: new Date().toISOString()
    };
  }

  // Optional authentication - works with or without auth
  @UseGuards(OptionalAuthGuard)
  @Get('optional')
  getOptionalData(@Req() request: any) {
    const user: User = request.user; // May be undefined if not authenticated
    
    if (user) {
      return {
        message: `Hello ${user.email}!`,
        authenticated: true,
        user: {
          id: user.id,
          email: user.email,
          role: user.role
        }
      };
    } else {
      return {
        message: 'Hello anonymous user!',
        authenticated: false
      };
    }
  }
}

// =============================================================================
// STEP 2: Create your application module
// =============================================================================

@Module({
  imports: [
    // Import the auth module using environment variables
    AuthModule.forRoot(),
    
    // Alternative: Manual configuration
    // AuthModule.register({
    //   encryptionKey: 'your-64-char-hex-encryption-key',
    //   externalAuthServiceUrl: 'https://auth.example.com/api',
    //   cookieNames: {
    //     accessToken: 'access_token',
    //     refreshToken: 'refresh_token',
    //   },
    //   requestTimeout: 10000,
    //   enableDebugLogging: false,
    // }),
  ],
  controllers: [AppController],
})
export class AppModule {}

// =============================================================================
// STEP 3: Bootstrap your application
// =============================================================================

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // IMPORTANT: Enable cookie parsing middleware
  app.use(cookieParser());
  
  // Optional: Enable CORS if needed
  app.enableCors({
    origin: true,
    credentials: true, // Important for cookies
  });
  
  // Optional: Add global prefix
  app.setGlobalPrefix('api');
  
  await app.listen(3000);
  console.log('Application is running on: http://localhost:3000');
  console.log('Auth endpoints available:');
  console.log('  GET /api/auth/me - Get current user (local JWT processing)');
  console.log('  GET /api/auth/users - Get all users (external service)');
  console.log('  GET /api/auth/users/:id - Get user by ID (external service)');
  console.log('');
  console.log('Example endpoints:');
  console.log('  GET /api/public - Public endpoint');
  console.log('  GET /api/protected - Protected endpoint (requires auth)');
  console.log('  GET /api/optional - Optional auth endpoint');
}

bootstrap();

// =============================================================================
// STEP 4: Environment Variables (.env file)
// =============================================================================

/*
Create a .env file in your project root with:

ACCESS_TOKEN_ENCRYPTION_KEY=your-64-char-hex-encryption-key
EXTERNAL_AUTH_SERVICE_URL=https://auth.example.com/api
JWKS_URI=https://auth.example.com/.well-known/jwks.json
ACCESS_TOKEN_COOKIE_NAME=access_token
REFRESH_TOKEN_COOKIE_NAME=refresh_token
AUTH_REQUEST_TIMEOUT=10000
AUTH_DEBUG_LOGGING=false
JWT_ISSUER=https://auth.example.com
JWT_AUDIENCE=your-app
JWT_SKIP_VERIFICATION=false
*/

// =============================================================================
// STEP 5: Package.json Dependencies
// =============================================================================

/*
Add these dependencies to your package.json:

{
  "dependencies": {
    "@nestjs/common": "^10.0.0",
    "@nestjs/core": "^10.0.0",
    "@nestjs/platform-express": "^10.0.0",
    "@nestjs/axios": "^3.0.0",
    "@nestjs/swagger": "^7.0.0",
    "@ngnair/auth-integration-package": "^1.0.0",
    "cookie-parser": "^1.4.6",
    "reflect-metadata": "^0.1.13",
    "rxjs": "^7.8.1"
  },
  "devDependencies": {
    "@types/cookie-parser": "^1.4.4",
    "@types/node": "^20.0.0",
    "typescript": "^5.1.3"
  }
}
*/

// =============================================================================
// TESTING THE INTEGRATION
// =============================================================================

/*
1. Start your application:
   npm run start

2. Test public endpoint:
   curl http://localhost:3000/api/public

3. Test protected endpoint (should return 401):
   curl http://localhost:3000/api/protected

4. Test with authentication cookie:
   curl -H "Cookie: access_token=your-encrypted-token" http://localhost:3000/api/auth/me

5. Test protected endpoint with auth:
   curl -H "Cookie: access_token=your-encrypted-token" http://localhost:3000/api/protected
*/
