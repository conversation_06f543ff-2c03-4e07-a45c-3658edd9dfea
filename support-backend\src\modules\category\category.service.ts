import { Injectable, NotFoundException, ConflictException } from "@nestjs/common";
import { Prisma, CategoryType } from "@prisma/client";
import { PrismaService } from "../../prisma/prisma.service";
import { CreateCategoryDto } from "./dto/create-category.dto";
import { UpdateCategoryDto } from "./dto/update-category.dto";
import {
  CategoryPayload,
  categorySelect,
  CreateCategoryParams
} from "./types/category.types";

@Injectable()
export class CategoryService {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: CreateCategoryParams | CreateCategoryDto): Promise<CategoryPayload> {
    // Support both DTO and Params for flexibility
    const createInput: Prisma.CategoryCreateInput = {
      name: (data as any).name,
      description: (data as any).description || null,
      type: (data as any).type || CategoryType.internal,
      autoAssignTo: (data as any).autoAssignTo || [],
      timeoutMinutes: (data as any).timeoutMinutes || 60,
      escalateTo: (data as any).escalateTo || null
    };
    try {
      return await this.prisma.category.create({
        data: createInput,
        select: categorySelect
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === "P2002") {
        throw new ConflictException("A category with this name already exists");
      }
      throw error;
    }
  }

  async findMany(params: {
    skip?: number;
    take?: number;
    where?: Prisma.CategoryWhereInput;
    orderBy?: Prisma.CategoryOrderByWithRelationInput;
  } = {}): Promise<CategoryPayload[]> {
    const { skip, take, where, orderBy } = params;
    return this.prisma.category.findMany({
      skip,
      take,
      where,
      orderBy: orderBy || { name: "asc" },
      select: categorySelect
    });
  }

  async findUnique(where: Prisma.CategoryWhereUniqueInput): Promise<CategoryPayload | null> {
    return this.prisma.category.findUnique({
      where,
      select: categorySelect
    });
  }

  async update(
    whereOrId: Prisma.CategoryWhereUniqueInput | string,
    data?: Prisma.CategoryUpdateInput | UpdateCategoryDto
  ): Promise<CategoryPayload> {
    // Support both (id, dto) and (where, data) signatures
    let where: Prisma.CategoryWhereUniqueInput;
    let updateData: Prisma.CategoryUpdateInput;
    if (typeof whereOrId === "string") {
      where = { id: whereOrId };
      updateData = { ...data } as Prisma.CategoryUpdateInput;
    } else {
      where = whereOrId;
      updateData = data as Prisma.CategoryUpdateInput;
    }
    // Optionally check existence
    const existing = await this.findUnique(where);
    if (!existing) {
      throw new NotFoundException(`Category not found`);
    }
    try {
      return await this.prisma.category.update({
        where,
        data: updateData,
        select: categorySelect
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === "P2002") {
        throw new ConflictException("A category with this name already exists");
      }
      throw error;
    }
  }

  async delete(whereOrId: Prisma.CategoryWhereUniqueInput | string): Promise<CategoryPayload> {
    let where: Prisma.CategoryWhereUniqueInput;
    if (typeof whereOrId === "string") {
      where = { id: whereOrId };
    } else {
      where = whereOrId;
    }
    // Optionally check existence
    const existing = await this.findUnique(where);
    if (!existing) {
      throw new NotFoundException(`Category not found`);
    }
    return this.prisma.category.delete({
      where,
      select: categorySelect
    });
  }
}
