import { Injectable, Logger } from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { PaymentProvider, PaymentTokenType, PaymentTokenStatus } from "@prisma/client";

// Type definitions for webhook payloads
interface ElavonWebhookPayload {
  reference_id: string;
  currency: string;
  amount: number;
  provider_reference_id: string;
  transaction_key?: string;
  device_id?: string;
  partner_id: string;
  status: string;
  response_code?: string;
  raw_response: {
    ssl_token: string;
    ssl_txn_id: string;
    ssl_card_type: string;
    ssl_approval_code?: string;
    ssl_card_number: string;
    ssl_exp_date: string;
    ssl_first_name?: string;
    ssl_last_name?: string;
    ssl_email?: string;
    ssl_country?: string;
    customerName?: string;
    customerId?: string;
    [key: string]: any;
  };
  ssl_token: string;
  transaction_id: string;
  customer_name: string;
  customer_id: string;
  card_type: string;
}

interface TSEPWebhookPayload {
  reference_id: string;
  currency: string;
  amount: number;
  provider_reference_id: string;
  transaction_key: string;
  device_id: string;
  partner_id: string;
  status: string;
  response_code: string;
  raw_response: {
    responseCode: string;
    status: string;
    message: string;
    expirationDate: string;
    cvv2: string;
    tsepToken: string;
    maskedCardNumber: string;
    cardType: string;
    transactionID: string;
    amount: string;
    cardHolderName: string;
    zipCode: string;
    transactionKey: string;
    deviceId: string;
    customerName?: string;
    customerId?: string;
    [key: string]: any;
  };
  tsep_token: string;
  transaction_id: string;
  customer_name: string;
  customer_id: string;
  card_type: string;
}

@Injectable()
export class WebhookService {
  private readonly logger = new Logger(WebhookService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Generic webhook handling method
   */
  async processWebhook(payload: Record<string, unknown>): Promise<Record<string, unknown>> {
    try {
      this.logger.log(`Processing webhook: ${JSON.stringify(payload)}`);

      // Determine webhook type based on payload structure
      const webhookType = this.determineWebhookType(payload);

      switch (webhookType) {
        case 'elavon':
          return await this.processElavonWebhook(payload as unknown as ElavonWebhookPayload);
        case 'tsep':
          return await this.processTSEPWebhook(payload as unknown as TSEPWebhookPayload);
        default:
          this.logger.warn(`Unknown webhook type for payload: ${JSON.stringify(payload)}`);
          return {
            status: "success",
            message: "Webhook received but not processed (unknown type)",
            timestamp: new Date().toISOString()
          };
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Error processing webhook: ${error.message}`, error.stack);
      } else {
        this.logger.error("Unknown error processing webhook");
      }
      throw error;
    }
  }

  /**
   * Determine webhook type based on payload structure
   */
  private determineWebhookType(payload: Record<string, unknown>): string {
    // Check for Elavon-specific fields
    if (payload.ssl_token && payload.raw_response &&
        (payload.raw_response as any)?.ssl_token) {
      return 'elavon';
    }

    // Check for TSEP-specific fields
    if (payload.tsep_token && payload.raw_response &&
        (payload.raw_response as any)?.tsepToken) {
      return 'tsep';
    }

    return 'unknown';
  }

  /**
   * Process Elavon webhook payload
   */
  private async processElavonWebhook(payload: ElavonWebhookPayload): Promise<Record<string, unknown>> {
    this.logger.log(`Processing Elavon webhook for customer: ${payload.customer_id}`);

    try {
      // Find or create customer
      const customer = await this.findOrCreateCustomer({
        customerId: payload.customer_id,
        customerName: payload.customer_name,
        email: payload.raw_response.ssl_email,
        firstName: payload.raw_response.ssl_first_name,
        lastName: payload.raw_response.ssl_last_name,
        country: payload.raw_response.ssl_country
      });

      // Create payment token
      const paymentToken = await this.prisma.paymentToken.create({
        data: {
          customerId: customer.id,
          tokenHash: this.hashToken(payload.ssl_token),
          externalTokenId: payload.ssl_token,
          paymentProvider: PaymentProvider.ELAVON,
          tokenType: PaymentTokenType.CARD,
          status: payload.status === '0' ? PaymentTokenStatus.ACTIVE : PaymentTokenStatus.SUSPENDED,

          // Payment method display info
          maskedInfo: payload.raw_response.ssl_card_number,
          paymentBrand: this.mapCardType(payload.card_type),
          expiresAt: this.parseElavonExpiryDate(payload.raw_response.ssl_exp_date),

          // Elavon specific fields
          elavonToken: payload.ssl_token,
          elavonTransactionId: payload.raw_response.ssl_txn_id,
          elavonReferenceId: payload.reference_id,
          elavonCardType: payload.card_type,
          elavonApprovalCode: payload.raw_response.ssl_approval_code,

          // Common transaction fields
          transactionAmount: payload.amount,
          currency: payload.currency,
          customerName: payload.customer_name,
          customerEmail: payload.raw_response.ssl_email,

          // Raw data storage
          rawWebhookData: payload as any,
          providerMetadata: payload.raw_response as any,

          // Finance integration
          financeId: null, // Will be set by finance service
          accountId: null, // Will be set by account service
        }
      });

      this.logger.log(`Created Elavon payment token: ${paymentToken.id}`);

      return {
        status: "success",
        message: "Elavon webhook processed successfully",
        timestamp: new Date().toISOString(),
        paymentTokenId: paymentToken.id,
        customerId: customer.id
      };
    } catch (error) {
      this.logger.error(`Error processing Elavon webhook: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Process TSEP webhook payload
   */
  private async processTSEPWebhook(payload: TSEPWebhookPayload): Promise<Record<string, unknown>> {
    this.logger.log(`Processing TSEP webhook for customer: ${payload.customer_id}`);

    try {
      // Find or create customer
      const customer = await this.findOrCreateCustomer({
        customerId: payload.customer_id,
        customerName: payload.customer_name,
        email: undefined, // TSEP doesn't provide email in the sample
        firstName: payload.raw_response.cardHolderName?.split(' ')[0],
        lastName: payload.raw_response.cardHolderName?.split(' ').slice(1).join(' '),
        country: 'US' // Default for TSEP
      });

      // Create payment token
      const paymentToken = await this.prisma.paymentToken.create({
        data: {
          customerId: customer.id,
          tokenHash: this.hashToken(payload.tsep_token),
          externalTokenId: payload.tsep_token,
          paymentProvider: PaymentProvider.TSEP,
          tokenType: PaymentTokenType.CARD,
          status: payload.status === 'PASS' ? PaymentTokenStatus.ACTIVE : PaymentTokenStatus.SUSPENDED,

          // Payment method display info
          maskedInfo: `**** **** **** ${payload.raw_response.maskedCardNumber}`,
          paymentBrand: this.mapCardType(payload.card_type),
          expiresAt: this.parseTSEPExpiryDate(payload.raw_response.expirationDate),

          // TSEP specific fields
          tsepToken: payload.tsep_token,
          tsepTransactionId: payload.raw_response.transactionID,
          tsepTransactionKey: payload.transaction_key,
          tsepDeviceId: payload.device_id,
          tsepCardType: payload.card_type,
          tsepResponseCode: payload.response_code,

          // Common transaction fields
          transactionAmount: payload.amount,
          currency: payload.currency,
          customerName: payload.customer_name,
          customerEmail: null, // TSEP doesn't provide email

          // Raw data storage
          rawWebhookData: payload as any,
          providerMetadata: payload.raw_response as any,

          // Finance integration
          financeId: null, // Will be set by finance service
          accountId: null, // Will be set by account service
        }
      });

      this.logger.log(`Created TSEP payment token: ${paymentToken.id}`);

      return {
        status: "success",
        message: "TSEP webhook processed successfully",
        timestamp: new Date().toISOString(),
        paymentTokenId: paymentToken.id,
        customerId: customer.id
      };
    } catch (error) {
      this.logger.error(`Error processing TSEP webhook: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Find or create customer based on webhook data
   */
  private async findOrCreateCustomer(customerData: {
    customerId: string;
    customerName: string;
    email?: string;
    firstName?: string;
    lastName?: string;
    country?: string;
  }) {
    // Try to find existing customer by external ID first
    let customer = await this.prisma.customer.findFirst({
      where: {
        externalId: customerData.customerId
      }
    });

    if (!customer && customerData.email) {
      // Try to find by email
      customer = await this.prisma.customer.findFirst({
        where: {
          email: customerData.email
        }
      });
    }

    if (!customer) {
      // Create new customer
      const [firstName, ...lastNameParts] = customerData.customerName.split(' ');
      const lastName = lastNameParts.join(' ') || '';

      customer = await this.prisma.customer.create({
        data: {
          externalId: customerData.customerId,
          firstName: customerData.firstName || firstName,
          lastName: customerData.lastName || lastName,
          email: customerData.email || `${customerData.customerId}@placeholder.com`,
          status: 'ACTIVE',
          type: 'INDIVIDUAL',
        }
      });

      this.logger.log(`Created new customer: ${customer.id} for external ID: ${customerData.customerId}`);
    } else {
      this.logger.log(`Found existing customer: ${customer.id} for external ID: ${customerData.customerId}`);
    }

    return customer;
  }

  /**
   * Hash token for secure storage
   */
  private hashToken(token: string): string {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  /**
   * Map card type codes to readable names
   */
  private mapCardType(cardType: string): string {
    const cardTypeMap: Record<string, string> = {
      'DISC': 'Discover',
      'VISA': 'Visa',
      'MC': 'Mastercard',
      'AMEX': 'American Express',
      'V': 'Visa',
      'M': 'Mastercard',
      'A': 'American Express',
      'D': 'Discover'
    };

    return cardTypeMap[cardType] || cardType;
  }

  /**
   * Parse Elavon expiry date (MMYY format)
   */
  private parseElavonExpiryDate(expiryDate: string): Date | null {
    if (!expiryDate || expiryDate.length !== 4) {
      return null;
    }

    const month = parseInt(expiryDate.substring(0, 2), 10);
    const year = parseInt('20' + expiryDate.substring(2, 4), 10);

    if (month < 1 || month > 12) {
      return null;
    }

    // Set to last day of the month
    return new Date(year, month - 1, new Date(year, month, 0).getDate());
  }

  /**
   * Parse TSEP expiry date (MM/YYYY format)
   */
  private parseTSEPExpiryDate(expiryDate: string): Date | null {
    if (!expiryDate) {
      return null;
    }

    const parts = expiryDate.split('/');
    if (parts.length !== 2) {
      return null;
    }

    const month = parseInt(parts[0], 10);
    const year = parseInt(parts[1], 10);

    if (month < 1 || month > 12 || year < 2000) {
      return null;
    }

    // Set to last day of the month
    return new Date(year, month - 1, new Date(year, month, 0).getDate());
  }
}
