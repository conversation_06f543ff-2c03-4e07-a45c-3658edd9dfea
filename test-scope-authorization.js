#!/usr/bin/env node

const https = require('https');
const http = require('http');
const { URL } = require('url');

// Configuration
const BASE_URL = 'http://ng-support-local.dev.dev1.ngnair.com:3040';
const ACCESS_TOKEN = 'ZVZxY3hQRFlueDNZYzhLbU9nZGZnYVlQUllNenBHdGI5R2NLYnd2b2ZLSTNEdWZ0TThFL2M5ODBCNEgrd2daVDNHaU9SVCtnTFZZTUtNZlVETFpvTGU3Z0VDSURkamU4YU5nSXVJQTZhSTYxRU84d1VJZG5zZ0c5a2dWVWY3SVRkUWQySi83WEQzWFhaTkN5UEdDYW5JUFJQb015QWY4dENsaUl0NEtlalBtWG9SeFY0N04xSllJcnMxWnU0MktyZjFjakI5MHBKM3ZkUXNvRXY3d3M5OWUxcWFxdURFdEhhdTlkRjVSWGErWHBNalVUcjJlQ05JWEpPTEZjRG9NVVJ0Z01SUzZuU3k5YUtwKzNIVDBwZEFPdjJRZWVvbHIvdHUzbEZjcWdpTGY3dERqQ1FKWjZYU2tCVXVmNTN0WHF2c0RXdjJoOExQNnB3RTRybnFmT0VpQWhsWHpGRzFLTW10QXRHQTlyUk5yVHJMUENHd3NrOXB6Y04yWVZJbllQVHJZOGE1SEhHSSsrN0ZoK2hlS3pyOHBqRFRIaEZmVTdBMnFmQ2Z5L2lKVFEzdk9hankzTjNJdDdoNjRKaDVlSnRtcFlvdFRWMmIrY1RWZVRpZzV0UzMyRXpsenBUNEhFeER2RTdsV1pDeVJXL1JyNWh1ZkhwczhmbXJHMm9SYW9BUEVLTEtYRW4xV0wvODFtTm14Q0IyY01QUzNDS05PcFNqb1YxQ1ZhRmVUS2dkL0crOUk3c2ZXTWtRZjM4VHdvYjdPQ0cxUy9FV1luL0MreVlGVXFZMUF2dlM0K1JmWnZhenhYRjNpUW92bGpvQzFVMXlpblR5RFMvWHM3LzhTcG8rcWk3Z1R3bTVPM0FzNE1sY1EzeTlGQ1pRM01DRThDTmJVZ3NsanE2b2FnRXZMSkRtUno5c1cvUk9EMzZIcnJqdFo4MVNXSEZ5MVo5dGRTemp6TGlRL2dMQzlWSG1OSUJCZkFBbUtXR2tGNjVieDVXZFhOS2FPb3lZc010SWVOSVlGcmpQUjlnMlgzNnJ2d1FSMC9EbEdhaVM0d0NxbEZZMkd6QnFLbkZMbVhuclRrNFU4ZTdMcDJDbjFQZ3NmdWFNUEM2RDAzUkZOU0dyMzlTSjBQSnlTNUVPU1hjN2t4eFAxTGR6TFRRdnFrOEdxNW1zNi92WnF5ZnFMN05aTEUyWkhXQkRSL2N5UVp2dkVRR1RSZjJjd3NCRnlVVUxaMkc5WVgvcUtrN1dZZmFyWThMYTRhbmdpbG9XUjE4eFFYMENFaVRtV1lOc0taYWI3OEdsQ3BqbEtuSTBOeCtlSVM3UGFjRWtnMjUzdk5EeEcwbC9DTGUwb3UxcnVpZlBiZVU5R010WERORU5LYlJWZEJ5SXY1em84Y29IMDFKalV1OE1BOG1Fc0UwZk9UNDZqamdRbmowNFRzTGVKL0h6VmpBZ3BlSm9zUXk1dHdHVnBFdmQ2UmtRVXpGdkp5aUpSazByTDVaQT09LS1McFU1RTE2RUpKMDhWcDFkLS12Rzc0Tnd5akNndjFSY1FZRmlEMzRRPT0%3D';

console.log('🐛 Testing Scope-Based Authorization Issues');
console.log('==========================================');
console.log(`Base URL: ${BASE_URL}`);
console.log(`Access Token: ${ACCESS_TOKEN.substring(0, 50)}...`);
console.log('');

// Helper function to make HTTP requests
function makeRequest(endpoint, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const url = new URL(endpoint, BASE_URL);
        const options = {
            hostname: url.hostname,
            port: url.port,
            path: url.pathname + url.search,
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Cookie': `access_token=${ACCESS_TOKEN}`,
                'User-Agent': 'Node.js-Test-Client/1.0'
            }
        };

        if (data) {
            const jsonData = JSON.stringify(data);
            options.headers['Content-Length'] = Buffer.byteLength(jsonData);
        }

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonBody = JSON.parse(body);
                    resolve({
                        success: res.statusCode >= 200 && res.statusCode < 300,
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        data: jsonBody,
                        headers: res.headers
                    });
                } catch (e) {
                    resolve({
                        success: false,
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        data: body,
                        headers: res.headers,
                        parseError: e.message
                    });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

// Helper function to display results
function displayResult(testName, result) {
    console.log(`\n📋 ${testName}`);
    console.log('─'.repeat(50));
    
    if (result.success) {
        console.log(`✅ SUCCESS (${result.status})`);
    } else {
        console.log(`❌ ERROR (${result.status})`);
    }
    
    console.log('Response:', JSON.stringify(result.data, null, 2));
    
    if (result.parseError) {
        console.log(`Parse Error: ${result.parseError}`);
        console.log(`Raw Response: ${result.data}`);
    }
}

// Helper function to analyze ticket data population
function analyzeTicketData(ticket) {
    console.log('\n📊 TICKET DATA POPULATION ANALYSIS');
    console.log('─'.repeat(50));
    
    const fields = {
        'firstName': ticket.firstName,
        'lastName': ticket.lastName,
        'email': ticket.email,
        'partnerOrgId': ticket.partnerOrgId,
        'partnerUserId': ticket.partnerUserId,
        'partnerRole': ticket.partnerRole
    };
    
    const nullFields = [];
    const populatedFields = [];
    
    for (const [field, value] of Object.entries(fields)) {
        if (value === null || value === undefined) {
            nullFields.push(field);
            console.log(`❌ ${field}: ${value} (NULL)`);
        } else {
            populatedFields.push(field);
            console.log(`✅ ${field}: ${value}`);
        }
    }
    
    console.log(`\n📈 Summary: ${populatedFields.length}/${Object.keys(fields).length} fields populated`);
    if (nullFields.length > 0) {
        console.log(`🚨 NULL FIELDS: ${nullFields.join(', ')}`);
    }
    
    return { nullFields, populatedFields, fields };
}

// Helper function to analyze authorization
function analyzeAuthorization(testName, result, shouldBeBlocked = false) {
    console.log(`\n🛡️ AUTHORIZATION ANALYSIS: ${testName}`);
    console.log('─'.repeat(50));
    
    if (shouldBeBlocked) {
        if (!result.success && (result.status === 401 || result.status === 403)) {
            console.log('✅ CORRECT: Request properly blocked');
            console.log(`   Status: ${result.status} (Expected: 401 or 403)`);
            return true;
        } else if (result.success) {
            console.log('❌ AUTHORIZATION BYPASS: Request succeeded when it should be blocked!');
            console.log(`   Status: ${result.status} (Expected: 401 or 403)`);
            return false;
        } else {
            console.log('❓ UNEXPECTED ERROR: Request failed but not due to authorization');
            console.log(`   Status: ${result.status}`);
            return false;
        }
    } else {
        if (result.success) {
            console.log('✅ CORRECT: Request allowed as expected');
            console.log(`   Status: ${result.status}`);
            return true;
        } else {
            console.log('❌ UNEXPECTED BLOCK: Request blocked when it should be allowed');
            console.log(`   Status: ${result.status}`);
            return false;
        }
    }
}

// Main test function
async function runTests() {
    const results = {};
    
    try {
        // Test 1: Debug Authentication
        console.log('\n🔍 TEST 1: Debug Authentication');
        console.log('═'.repeat(50));
        
        try {
            const authResult = await makeRequest('/partner-test/debug-auth');
            displayResult('Authentication Debug', authResult);
            results.auth = authResult;
            
            if (authResult.success && authResult.data.debug) {
                const debug = authResult.data.debug;
                console.log('\n🔍 Authentication Status:');
                console.log(`   Has Cookies: ${debug.hasCookies}`);
                console.log(`   Has User: ${debug.hasUser}`);
                console.log(`   Has Scope Context: ${debug.hasScopeContext}`);
                if (debug.scopeContext) {
                    console.log(`   User Scope: ${debug.scopeContext.scope}`);
                    console.log(`   Partner Info: ${debug.scopeContext.partnerInfo ? 'Available' : 'Missing'}`);
                }
            }
        } catch (error) {
            console.log(`❌ Authentication debug failed: ${error.message}`);
        }

        // Test 2: Get Categories (Should succeed)
        console.log('\n\n📋 TEST 2: Get Categories (Should Succeed)');
        console.log('═'.repeat(50));
        
        const categoriesResult = await makeRequest('/categories');
        displayResult('Get Categories', categoriesResult);
        results.getCategories = categoriesResult;
        analyzeAuthorization('Get Categories', categoriesResult, false);

        // Test 3: Create Category (Should be blocked)
        console.log('\n\n📝 TEST 3: Create Category (Should Be Blocked)');
        console.log('═'.repeat(50));
        
        const categoryData = {
            name: 'Test Category - Should Be Blocked',
            description: 'Testing authorization for partner:support:user scope'
        };
        
        const createCategoryResult = await makeRequest('/categories', 'POST', categoryData);
        displayResult('Create Category', createCategoryResult);
        results.createCategory = createCategoryResult;
        analyzeAuthorization('Create Category', createCategoryResult, true);

        // Test 4: Create Ticket (Data Population Test)
        console.log('\n\n🎫 TEST 4: Create Ticket (Data Population Test)');
        console.log('═'.repeat(50));
        
        if (categoriesResult.success && categoriesResult.data.length > 0) {
            const ticketData = {
                subject: 'Test Ticket - Data Population Debug',
                description: 'Testing automatic population of user data and partner information fields',
                categoryId: categoriesResult.data[0].id,
                priority: 'MEDIUM'
            };
            
            const createTicketResult = await makeRequest('/tickets', 'POST', ticketData);
            displayResult('Create Ticket', createTicketResult);
            results.createTicket = createTicketResult;
            
            if (createTicketResult.success && createTicketResult.data) {
                const analysis = analyzeTicketData(createTicketResult.data);
                results.ticketAnalysis = analysis;
            }
        } else {
            console.log('❌ Cannot test ticket creation - no categories available');
        }

        // Test 5: Scope Resolution Test
        console.log('\n\n🔍 TEST 5: Scope Resolution Test');
        console.log('═'.repeat(50));
        
        try {
            const scopeResult = await makeRequest('/partner-test/test-flow?user_id=f7b98e6f-95af-4d54-9c53-312ada49ba6e');
            displayResult('Scope Resolution', scopeResult);
            results.scopeResolution = scopeResult;
            
            if (scopeResult.success && scopeResult.data) {
                console.log('\n🔍 Scope Resolution Analysis:');
                console.log(`   Partners Step Success: ${scopeResult.data.partnersStep?.success}`);
                console.log(`   Scopes Step Success: ${scopeResult.data.scopesStep?.success}`);
                if (scopeResult.data.partnersStep?.firstPartner) {
                    console.log(`   Partner Found: ${scopeResult.data.partnersStep.firstPartner.public_uid}`);
                }
            }
        } catch (error) {
            console.log(`❌ Scope resolution test failed: ${error.message}`);
        }

    } catch (error) {
        console.log(`❌ Test execution failed: ${error.message}`);
    }

    // Final Analysis
    console.log('\n\n📊 FINAL ANALYSIS');
    console.log('═'.repeat(50));
    
    console.log('\n🔍 Issues Identified:');
    
    // Check authentication issues
    if (!results.auth?.success || !results.auth?.data?.debug?.hasScopeContext) {
        console.log('❌ CRITICAL: Authentication or scope context not working');
    }
    
    // Check data population issues
    if (results.ticketAnalysis?.nullFields?.length > 0) {
        console.log(`❌ DATA POPULATION: ${results.ticketAnalysis.nullFields.length} fields are null`);
        console.log(`   Null fields: ${results.ticketAnalysis.nullFields.join(', ')}`);
    }
    
    // Check authorization bypass
    if (results.createCategory?.success) {
        console.log('❌ AUTHORIZATION BYPASS: Category creation should be blocked for partner:support:user');
    }
    
    console.log('\n🔧 Recommended Actions:');
    console.log('1. Check server logs for authentication middleware errors');
    console.log('2. Verify JWT token decryption and user extraction');
    console.log('3. Ensure scope resolution service is properly called');
    console.log('4. Validate user data service API integration');
    console.log('5. Check scope guard implementation for category endpoints');
    
    console.log('\n🏁 Test completed!');
}

// Run the tests
runTests().catch(console.error);
