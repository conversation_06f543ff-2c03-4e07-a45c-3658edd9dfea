/**
 * @ngnair/auth-integration-package
 * 
 * Complete authentication module package with Rails-compatible AES-256-GCM cookie decryption
 * and JWT processing for NestJS applications.
 * 
 * Features:
 * - Rails-compatible AES-256-GCM cookie decryption
 * - Local JWT processing for /auth/me endpoint (no external API calls)
 * - External service forwarding for /auth/users endpoints
 * - Comprehensive TypeScript support
 * - Flexible configuration options
 * - Built-in authentication guards
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

// Core module and service exports
export { AuthModule } from './auth.module';
export { AuthService } from './auth.service';
export { AuthController } from './auth.controller';
export { AuthResolver } from './resolvers/auth.resolver';

// Guard exports
export { AuthGuard, OptionalAuthGuard } from './guards/auth.guard';

// Type exports
export {
  User,
  JWTPayload,
  DecryptedTokens,
  CookieNames,
  AuthErrorResponse,
  AuthMeResponse,
  ExternalUserResponse,
} from './types/auth.types';

// GraphQL type exports
export { JWTPayloadGraphQL, UserGraphQL } from './types/graphql.types';

// Configuration exports
export {
  AuthConfig,
  DEFAULT_AUTH_CONFIG,
  AUTH_CONFIG_ENV_MAPPING,
} from './types/auth-config.interface';

// Re-export commonly used decorators and types from NestJS for convenience
export { UseGuards } from '@nestjs/common';
export { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';

/**
 * Package version
 */
export const VERSION = '1.0.0';

/**
 * Package information
 */
export const PACKAGE_INFO = {
  name: '@ngnair/auth-integration-package',
  version: VERSION,
  description: 'Complete authentication module package with Rails-compatible AES-256-GCM cookie decryption and JWT processing for NestJS applications',
  author: 'NGNair Development Team',
  license: 'MIT',
} as const;
