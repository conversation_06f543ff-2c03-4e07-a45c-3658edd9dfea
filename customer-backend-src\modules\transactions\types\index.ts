export interface TransactionRequest {
  transactionId: string;
  amount: number;
  currency: string;
  description?: string;
}

export interface OtpVerificationRequest {
  transactionId: string;
  phoneNumber: string;
  otp: string;
}

export interface PaymentDetails {
  routingNumber: string;
  cvv: string;
  accountNumber: string;
  expiryDate: string;
}

export interface TransactionResponse {
  transactionId: string;
  status: 'pending' | 'otp_sent' | 'verified' | 'completed' | 'failed';
  message: string;
  userDetails?: {
    id: string;
    email: string;
    password?: string;
    firstName?: string;
    lastName?: string;
    phone?: {
      number: string;
      masked: string;
    };
    country?: string;
    verifiedEmail?: boolean;
    verifiedPhone?: boolean;
    totpSecret?: string;
    role?: string;
    createdAt?: string;
    partnerId?: string | string[];
    mfaEnabled?: boolean;
    active?: boolean;
    accountId?: string;
    isAdmin: boolean;
    permissions: string[];
  };
  paymentDetails?: PaymentDetails;
}
