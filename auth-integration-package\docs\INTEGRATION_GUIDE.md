# Complete Integration Guide

## 📁 Project Structure

When integrating the `@ngnair/auth-integration-package` into your NestJS application, your project should follow this structure:

```
your-nestjs-app/
├── src/
│   ├── app.module.ts              # ✅ Import AuthModule here
│   ├── main.ts                    # ✅ Enable cookieParser() here
│   ├── controllers/
│   │   ├── app.controller.ts      # Your application controllers
│   │   └── user.controller.ts     # Example: Custom user controller
│   ├── services/
│   │   ├── app.service.ts         # Your application services
│   │   └── user.service.ts        # Example: Custom user service
│   ├── guards/                    # Optional: Custom guards
│   │   └── roles.guard.ts         # Example: Role-based guard
│   ├── decorators/                # Optional: Custom decorators
│   │   └── current-user.decorator.ts
│   └── types/                     # Optional: Custom types
│       └── user.types.ts
├── config/
│   ├── .env.example               # ✅ Copy from package
│   └── auth.config.ts             # Optional: Custom config
├── .env                           # ✅ Your environment variables
├── package.json                   # ✅ Add package dependencies
├── tsconfig.json                  # TypeScript configuration
└── README.md                      # Your project documentation
```

## 🚀 Step-by-Step Integration

### Step 1: Install Dependencies

```bash
# Install the auth package
npm install @ngnair/auth-integration-package

# Install required peer dependencies
npm install @nestjs/common @nestjs/core @nestjs/axios @nestjs/swagger
npm install axios class-transformer class-validator jwks-client cookie-parser

# Install type definitions
npm install --save-dev @types/cookie-parser
```

### Step 2: Environment Configuration

Copy the example environment file:

```bash
cp node_modules/@ngnair/auth-integration-package/config/.env.example .env
```

Update your `.env` file with your actual values:

```env
# Required
ACCESS_TOKEN_ENCRYPTION_KEY=your-actual-64-char-hex-key
EXTERNAL_AUTH_SERVICE_URL=https://your-auth-service.com/api

# Optional but recommended
JWKS_URI=https://your-auth-service.com/.well-known/jwks.json
JWT_ISSUER=https://your-auth-service.com
JWT_AUDIENCE=your-app-name
```

### Step 3: Update main.ts

```typescript
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import * as cookieParser from 'cookie-parser';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // ✅ REQUIRED: Enable cookie parsing
  app.use(cookieParser());
  
  // Optional: Enable CORS with credentials
  app.enableCors({
    origin: true,
    credentials: true, // Important for cookies
  });
  
  await app.listen(3000);
}
bootstrap();
```

### Step 4: Update app.module.ts

```typescript
import { Module } from '@nestjs/common';
import { AuthModule } from '@ngnair/auth-integration-package';
import { AppController } from './app.controller';

@Module({
  imports: [
    // ✅ Import AuthModule using environment variables
    AuthModule.forRoot(),
  ],
  controllers: [AppController],
})
export class AppModule {}
```

### Step 5: Use in Controllers

```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { AuthGuard, User } from '@ngnair/auth-integration-package';

@Controller()
export class AppController {
  
  // ✅ Protected endpoint
  @UseGuards(AuthGuard)
  @Get('protected')
  getProtectedData(@Req() request: any) {
    const user: User = request.user;
    return { message: `Hello ${user.email}!` };
  }
}
```

## 🔧 Configuration Options

### Option 1: Environment Variables (Recommended)

```typescript
// app.module.ts
AuthModule.forRoot() // Reads from environment variables
```

### Option 2: Direct Configuration

```typescript
// app.module.ts
AuthModule.register({
  encryptionKey: 'your-64-char-hex-key',
  externalAuthServiceUrl: 'https://auth.example.com/api',
  cookieNames: {
    accessToken: 'access_token',
    refreshToken: 'refresh_token',
  },
})
```

### Option 3: Async Configuration with ConfigService

```typescript
// app.module.ts
import { ConfigService } from '@nestjs/config';

AuthModule.registerAsync({
  useFactory: (configService: ConfigService) => ({
    encryptionKey: configService.get('ACCESS_TOKEN_ENCRYPTION_KEY'),
    externalAuthServiceUrl: configService.get('EXTERNAL_AUTH_SERVICE_URL'),
    // ... other config
  }),
  inject: [ConfigService],
})
```

## 🛡️ Authentication Guards

### Basic Authentication

```typescript
import { UseGuards } from '@nestjs/common';
import { AuthGuard } from '@ngnair/auth-integration-package';

@UseGuards(AuthGuard)
@Get('protected')
protectedEndpoint() {
  return { message: 'This requires authentication' };
}
```

### Optional Authentication

```typescript
import { OptionalAuthGuard } from '@ngnair/auth-integration-package';

@UseGuards(OptionalAuthGuard)
@Get('optional')
optionalEndpoint(@Req() request: any) {
  const user = request.user; // May be undefined
  return { 
    message: user ? `Hello ${user.email}` : 'Hello anonymous' 
  };
}
```

## 📊 API Endpoints Provided

The package automatically provides these endpoints:

### `/auth/me` - Local JWT Processing
- **Method**: GET
- **Authentication**: Required (cookies)
- **External Calls**: None
- **Purpose**: Get current user from JWT payload

```bash
curl -H "Cookie: access_token=encrypted-token" \
     http://localhost:3000/auth/me
```

### `/auth/users` - External Service Proxy
- **Method**: GET
- **Authentication**: Required (cookies)
- **External Calls**: Yes
- **Purpose**: Get all users from external service

```bash
curl -H "Cookie: access_token=encrypted-token" \
     http://localhost:3000/auth/users
```

### `/auth/users/:id` - External Service Proxy
- **Method**: GET
- **Authentication**: Required (cookies)
- **External Calls**: Yes
- **Purpose**: Get specific user from external service

```bash
curl -H "Cookie: access_token=encrypted-token" \
     http://localhost:3000/auth/users/user-id-123
```

## 🔍 Testing Your Integration

### 1. Test Public Endpoints

```bash
curl http://localhost:3000/health
# Should return 200 OK
```

### 2. Test Authentication Required

```bash
curl http://localhost:3000/auth/me
# Should return 401 Unauthorized
```

### 3. Test with Valid Token

```bash
curl -H "Cookie: access_token=your-encrypted-token" \
     http://localhost:3000/auth/me
# Should return user information
```

### 4. Test Protected Endpoints

```bash
curl -H "Cookie: access_token=your-encrypted-token" \
     http://localhost:3000/protected
# Should return protected data
```

## 🐛 Common Issues & Solutions

### Issue: "Missing access token"
**Solution**: Ensure `cookieParser()` is enabled in `main.ts`

### Issue: "Failed to decrypt authentication token"
**Solution**: Verify `ACCESS_TOKEN_ENCRYPTION_KEY` matches the encryption key used to create cookies

### Issue: "Authentication failed"
**Solution**: Check JWT verification settings and JWKS URI

### Issue: External service timeout
**Solution**: Increase `AUTH_REQUEST_TIMEOUT` or check external service availability

## 📈 Performance Considerations

### Local vs External Processing

- **`/auth/me`**: Fast (local JWT processing, no external calls)
- **`/auth/users`**: Slower (external API calls, network dependent)

### Caching Recommendations

```typescript
// Example: Cache external user data
@Injectable()
export class CachedUserService {
  private cache = new Map();
  
  async getUsers(cookies: Record<string, string>) {
    const cacheKey = 'all-users';
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    const users = await this.authService.getAllUsers(cookies);
    this.cache.set(cacheKey, users);
    
    // Clear cache after 5 minutes
    setTimeout(() => this.cache.delete(cacheKey), 5 * 60 * 1000);
    
    return users;
  }
}
```

## 🔒 Security Best Practices

1. **Always use HTTPS in production**
2. **Keep encryption keys secure and rotate regularly**
3. **Enable JWT verification in production**
4. **Set appropriate cookie security flags**
5. **Monitor authentication logs**
6. **Implement rate limiting on auth endpoints**

## 📚 Next Steps

1. **Customize**: Add role-based access control
2. **Extend**: Create custom guards and decorators
3. **Monitor**: Add logging and metrics
4. **Scale**: Implement caching for external calls
5. **Secure**: Add additional security layers
