import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '../../config/config.module';
import { AuthSharedService } from './auth.service';
import { AuthSharedGuard } from './auth.guard';
import { AuthSharedMiddleware } from './auth.middleware';
import { AuthSharedController } from './auth.controller';
import { ApiGuard, AdminGuard } from './guards';

@Module({
  imports: [ConfigModule, HttpModule],
  providers: [AuthSharedService, AuthSharedGuard, AuthSharedMiddleware, ApiGuard, AdminGuard],
  controllers: [AuthSharedController],
  exports: [AuthSharedService, AuthSharedGuard, AuthSharedMiddleware, ApiGuard, AdminGuard],
})
export class AuthSharedModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AuthSharedMiddleware)
      .forRoutes('*'); // Apply to all routes
  }
}

// For manual integration without NestJS module system
export class AuthModuleStandalone {
  private authService: AuthSharedService;
  private authGuard: AuthSharedGuard;

  constructor(config: {
    authJwksUrl?: string;
    encryptionKey?: string;
  }) {
    // Note: For standalone usage, HttpService is not available
    // Use the regular NestJS module integration instead
    throw new Error('Standalone usage not supported with HttpService dependency. Use AuthSharedModule instead.');
  }

  getAuthService(): AuthSharedService {
    return this.authService;
  }

  getAuthGuard(): AuthSharedGuard {
    return this.authGuard;
  }
}

// Export everything for easy importing
export * from './auth.service';
export { AuthSharedGuard, Roles, Permissions, Public } from './auth.guard';
export * from './auth.middleware';
export * from './auth.controller';
export * from './guards';
export { getCurrentUser } from './decorators/user.decorator';
export * from './types/auth.types';
export * from './types';
