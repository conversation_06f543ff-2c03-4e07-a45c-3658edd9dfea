import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';

export class FileUploadDto {
  @ApiProperty({ required: false, description: "The ID of the ticket to attach the file to" })
  @IsString()
  @IsOptional()
  ticketId?: string;

  @ApiProperty({ required: false, description: "The ID of the comment to attach the file to" })
  @IsString()
  @IsOptional()
  commentId?: string;
}

export class FileResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  filename: string;

  @ApiProperty()
  fileSize: number;

  @ApiProperty()
  fileUrl: string;

  @ApiProperty()
  mimeType: string;

  @ApiProperty()
  uploadedBy: string;

  @ApiProperty({ required: false, nullable: true })
  ticketId: string | null;

  @ApiProperty({ required: false, nullable: true })
  commentId: string | null;

  @ApiProperty()
  uploadedAt: Date;
}
