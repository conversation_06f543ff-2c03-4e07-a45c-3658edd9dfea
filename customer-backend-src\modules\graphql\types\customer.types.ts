import { ObjectType, Field, ID, registerEnumType } from '@nestjs/graphql';
import { CustomerStatus, CustomerType, AddressType, ContactType, PreferenceType, AuditAction } from '@prisma/client';

// Register Prisma enums with GraphQL
registerEnumType(CustomerStatus, {
  name: 'CustomerStatus',
  description: 'Customer status enumeration',
});

registerEnumType(CustomerType, {
  name: 'CustomerType',
  description: 'Customer type enumeration',
});

registerEnumType(AddressType, {
  name: 'AddressType',
  description: 'Address type enumeration',
});

registerEnumType(ContactType, {
  name: 'ContactType',
  description: 'Contact type enumeration',
});

registerEnumType(PreferenceType, {
  name: 'PreferenceType',
  description: 'Preference type enumeration',
});

registerEnumType(AuditAction, {
  name: 'AuditAction',
  description: 'Audit action enumeration',
});

@ObjectType()
export class Customer {
  @Field(() => ID)
  id: string;

  @Field({ nullable: true })
  externalId?: string;

  @Field()
  firstName: string;

  @Field()
  lastName: string;

  @Field()
  email: string;

  @Field({ nullable: true })
  phone?: string;

  @Field({ nullable: true })
  dateOfBirth?: Date;

  @Field({ nullable: true })
  companyName?: string;

  @Field({ nullable: true })
  taxId?: string;

  @Field({ nullable: true })
  businessType?: string;

  @Field(() => CustomerStatus)
  status: CustomerStatus;

  @Field(() => CustomerType)
  type: CustomerType;

  @Field()
  isEmailVerified: boolean;

  @Field()
  isPhoneVerified: boolean;

  @Field()
  isKycVerified: boolean;

  // Aliases for frontend compatibility
  @Field({ name: 'emailVerified' })
  get emailVerified(): boolean {
    return this.isEmailVerified;
  }

  @Field({ name: 'phoneVerified' })
  get phoneVerified(): boolean {
    return this.isPhoneVerified;
  }

  @Field({ name: 'kycVerified' })
  get kycVerified(): boolean {
    return this.isKycVerified;
  }

  @Field(() => [String])
  tags: string[];

  @Field({ nullable: true })
  notes?: string;

  @Field(() => [Address])
  addresses: Address[];

  @Field(() => [Contact])
  contacts: Contact[];

  @Field(() => [CustomerPreference])
  preferences: CustomerPreference[];

  @Field(() => [AuditLog], { nullable: true })
  auditLogs?: AuditLog[];

  @Field(() => [CustomerSegmentMember], { nullable: true })
  segmentMembers?: CustomerSegmentMember[];

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;

  @Field({ nullable: true })
  lastLoginAt?: Date;

  @Field({ nullable: true })
  deletedAt?: Date;
}

@ObjectType()
export class Address {
  @Field(() => ID)
  id: string;

  @Field()
  customerId: string;

  @Field(() => AddressType)
  type: AddressType;

  @Field({ nullable: true })
  label?: string;

  @Field()
  street1: string;

  @Field({ nullable: true })
  street2?: string;

  @Field()
  city: string;

  @Field()
  state: string;

  @Field()
  postalCode: string;

  @Field()
  country: string;

  @Field({ nullable: true })
  latitude?: number;

  @Field({ nullable: true })
  longitude?: number;

  @Field()
  isDefault: boolean;

  @Field()
  isVerified: boolean;

  @Field(() => Customer)
  customer: Customer;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;
}

@ObjectType()
export class Contact {
  @Field(() => ID)
  id: string;

  @Field()
  customerId: string;

  @Field(() => ContactType)
  type: ContactType;

  @Field({ nullable: true })
  label?: string;

  @Field()
  firstName: string;

  @Field()
  lastName: string;

  @Field({ nullable: true })
  email?: string;

  @Field({ nullable: true })
  phone?: string;

  @Field({ nullable: true })
  relationship?: string;

  @Field()
  isDefault: boolean;

  @Field()
  isVerified: boolean;

  @Field(() => Customer)
  customer: Customer;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;
}

@ObjectType()
export class CustomerPreference {
  @Field(() => ID)
  id: string;

  @Field()
  customerId: string;

  @Field(() => PreferenceType)
  type: PreferenceType;

  @Field()
  key: string;

  @Field()
  value: string;

  @Field({ nullable: true })
  description?: string;

  @Field()
  isActive: boolean;

  @Field(() => Customer)
  customer: Customer;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;
}

@ObjectType()
export class AuditLog {
  @Field(() => ID)
  id: string;

  @Field()
  customerId: string;

  @Field(() => AuditAction)
  action: AuditAction;

  @Field()
  entity: string;

  @Field({ nullable: true })
  entityId?: string;

  @Field({ nullable: true })
  description?: string;

  @Field(() => Customer)
  customer: Customer;

  @Field()
  createdAt: Date;
}

@ObjectType()
export class CustomerSegment {
  @Field(() => ID)
  id: string;

  @Field()
  name: string;

  @Field({ nullable: true })
  description?: string;

  @Field()
  isActive: boolean;

  @Field()
  isAutomatic: boolean;

  @Field(() => [CustomerSegmentMember])
  customers: CustomerSegmentMember[];

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;
}

@ObjectType()
export class CustomerSegmentMember {
  @Field(() => ID)
  id: string;

  @Field()
  customerId: string;

  @Field()
  segmentId: string;

  @Field()
  assignedAt: Date;

  @Field({ nullable: true })
  assignedBy?: string;

  @Field()
  isActive: boolean;

  @Field(() => Customer)
  customer: Customer;

  @Field(() => CustomerSegment)
  segment: CustomerSegment;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;
}

@ObjectType()
export class PaginatedCustomers {
  @Field(() => [Customer])
  data: Customer[];

  @Field()
  total: number;

  @Field()
  page: number;

  @Field()
  limit: number;
}
