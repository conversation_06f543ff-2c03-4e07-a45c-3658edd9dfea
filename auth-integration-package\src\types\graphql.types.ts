import { ObjectType, Field } from '@nestjs/graphql';

/**
 * GraphQL JWT Payload type matching Rails JWT specification
 * This type represents the exact structure returned by Rails authentication
 */
@ObjectType()
export class JWTPayloadGraphQL {
  // Standard JWT claims
  @Field()
  iss: string; // Issuer

  @Field()
  sub: string; // Subject (User ID)

  @Field()
  aud: string; // Audience

  @Field()
  exp: number; // Expires at (Unix timestamp)

  @Field()
  iat: number; // Issued at (Unix timestamp)

  @Field()
  jti: string; // JWT ID (unique identifier)

  @Field()
  email: string; // User email

  // NGNair-specific fields
  @Field({ nullable: true })
  sid?: string; // Session ID

  @Field({ nullable: true })
  azp?: string; // Authorized party

  @Field({ nullable: true })
  ent_set?: string; // Entity set

  @Field({ nullable: true })
  perm_v?: number; // Permission version

  @Field(() => [String], { nullable: true })
  amr?: string[]; // Authentication methods reference

  @Field({ nullable: true })
  auth_time?: number; // Authentication time

  // Optional user fields (only if present in JWT)
  @Field({ nullable: true })
  first_name?: string;

  @Field({ nullable: true })
  last_name?: string;
}

/**
 * GraphQL User type matching external auth service response structure
 * This type represents the User objects returned by the external auth service
 */
@ObjectType()
export class UserGraphQL {
  @Field()
  id: string;

  @Field()
  email: string;

  @Field({ nullable: true })
  first_name?: string;

  @Field({ nullable: true })
  last_name?: string;

  // Optional fields that may not be present in external service response
  @Field({ nullable: true })
  username?: string;

  @Field({ nullable: true })
  firstName?: string;

  @Field({ nullable: true })
  lastName?: string;

  @Field({ nullable: true })
  role?: string;

  @Field(() => [String], { nullable: true })
  permissions?: string[];

  @Field({ nullable: true })
  createdAt?: string;

  @Field({ nullable: true })
  updatedAt?: string;

  // Additional fields from external auth service
  @Field({ nullable: true })
  phone?: string;

  @Field({ nullable: true })
  country?: string;

  @Field({ nullable: true })
  verifiedEmail?: boolean;

  @Field({ nullable: true })
  verifiedPhone?: boolean;

  @Field({ nullable: true })
  partnerId?: string;

  @Field({ nullable: true })
  mfaEnabled?: boolean;

  @Field({ nullable: true })
  active?: boolean;

  @Field({ nullable: true })
  accountId?: string;

  @Field({ nullable: true })
  isAdmin?: boolean;

  @Field(() => [Object], { nullable: true })
  accounts?: any[];

  @Field(() => [Object], { nullable: true })
  partners?: any[];

  @Field(() => [Object], { nullable: true })
  active_accounts?: any[];

  @Field(() => [Object], { nullable: true })
  active_partners?: any[];
}
