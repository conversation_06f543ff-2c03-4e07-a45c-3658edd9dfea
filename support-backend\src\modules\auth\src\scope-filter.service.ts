import { Injectable, Logger } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { 
  ScopeContext, 
  SupportScope, 
  TenantType,
  getScopePermissions,
  canViewAllTicketsInTenant 
} from './types/scope.types';

/**
 * Service for applying scope-based filtering to database queries
 */
@Injectable()
export class ScopeFilterService {
  private readonly logger = new Logger(ScopeFilterService.name);

  /**
   * Apply scope-based filtering to ticket queries
   */
  applyTicketFilters(scopeContext: ScopeContext, baseWhere: Prisma.TicketWhereInput = {}): Prisma.TicketWhereInput {
    const permissions = getScopePermissions(scopeContext.scope);
    let where: Prisma.TicketWhereInput = { ...baseWhere };

    // Apply soft delete filter (only global admin can see soft deleted items)
    if (!permissions.canViewSoftDeleted) {
      where.deletedAt = null;
    }

    // Apply scope-based filtering according to requirements
    if (scopeContext.scope === SupportScope.GLOBAL_SUPPORT_ADMIN) {
      // Global admin can see everything, no additional filters needed
      this.logger.log(`🌍 [SCOPE FILTER] Global admin access - no tenant filtering`);
    } else if (scopeContext.scope === SupportScope.PARTNER_SUPPORT_ADMIN) {
      // Partner admin: Return ALL tickets where partnerOrgId matches current user's partner public_uid
      const partnerPublicUid = scopeContext.partnerInfo?.public_uid;
      if (partnerPublicUid) {
        where.partnerOrgId = partnerPublicUid;
        this.logger.log(`🏢 [SCOPE FILTER] Partner admin access - filtering by partnerOrgId: ${partnerPublicUid}`);
      } else {
        this.logger.warn(`⚠️ [SCOPE FILTER] Partner admin missing partner public_uid, applying restrictive filter`);
        where.sub = scopeContext.userId; // Fallback to user's own tickets
      }
    } else if (scopeContext.scope === SupportScope.PARTNER_SUPPORT_USER) {
      // Partner user: Return ONLY tickets where BOTH partnerOrgId matches AND partnerUserId matches
      const partnerPublicUid = scopeContext.partnerInfo?.public_uid;
      if (partnerPublicUid) {
        const userFilter: Prisma.TicketWhereInput = {
          AND: [
            { partnerUserId: scopeContext.userId }, // Must be their own ticket (stored in partnerUserId)
            { partnerOrgId: partnerPublicUid }       // Must be within their partner (stored in partnerOrgId)
          ]
        };
        where = { ...where, ...userFilter };
        this.logger.log(`👤 [SCOPE FILTER] Partner user access - filtering by partnerUserId: ${scopeContext.userId} AND partnerOrgId: ${partnerPublicUid}`);
      } else {
        this.logger.warn(`⚠️ [SCOPE FILTER] Partner user missing partner public_uid, applying restrictive filter`);
        where.sub = scopeContext.userId; // Fallback to user's own tickets
      }
    } else {
      // For any other scope, deny access by applying impossible filter
      this.logger.warn(`❌ [SCOPE FILTER] Invalid scope ${scopeContext.scope} - denying access`);
      where.id = 'impossible-id-to-match'; // This will return no results
    }

    return where;
  }

  /**
   * Apply scope-based filtering to category queries
   */
  applyCategoryFilters(scopeContext: ScopeContext, baseWhere: Prisma.CategoryWhereInput = {}): Prisma.CategoryWhereInput {
    const permissions = getScopePermissions(scopeContext.scope);
    let where: Prisma.CategoryWhereInput = { ...baseWhere };

    // Apply soft delete filter (only global admin can see soft deleted items)
    if (!permissions.canViewSoftDeleted) {
      where.deletedAt = null;
    }

    // Categories are generally visible to all users, but soft delete rules apply
    this.logger.log(`📁 [SCOPE FILTER] Category access - applying soft delete filter only`);

    return where;
  }

  /**
   * Apply scope-based filtering to comment queries
   */
  applyCommentFilters(scopeContext: ScopeContext, baseWhere: Prisma.CommentWhereInput = {}): Prisma.CommentWhereInput {
    const permissions = getScopePermissions(scopeContext.scope);
    let where: Prisma.CommentWhereInput = { ...baseWhere };

    // Apply soft delete filter (only global admin can see soft deleted items)
    if (!permissions.canViewSoftDeleted) {
      where.deletedAt = null;
    }

    // Comments inherit access from their parent ticket
    // The ticket filtering will handle the scope-based access
    this.logger.log(`💬 [SCOPE FILTER] Comment access - applying soft delete filter only`);

    return where;
  }

  /**
   * Check if user can access a specific ticket
   */
  canAccessTicket(scopeContext: ScopeContext, ticket: any): boolean {
    const permissions = getScopePermissions(scopeContext.scope);

    // Check soft delete access
    if (ticket.deletedAt && !permissions.canViewSoftDeleted) {
      this.logger.log(`❌ [SCOPE FILTER] Access denied - ticket is soft deleted and user cannot view soft deleted items`);
      return false;
    }

    // Global admin can access everything
    if (scopeContext.scope === SupportScope.GLOBAL_SUPPORT_ADMIN) {
      this.logger.log(`✅ [SCOPE FILTER] Global admin access granted`);
      return true;
    }

    // Partner admin can access all tickets within their partner (using partnerOrgId field)
    if (scopeContext.scope === SupportScope.PARTNER_SUPPORT_ADMIN) {
      const partnerPublicUid = scopeContext.partnerInfo?.public_uid;
      const hasAccess = ticket.partnerOrgId === partnerPublicUid;
      this.logger.log(`${hasAccess ? '✅' : '❌'} [SCOPE FILTER] Partner admin access - ticket partnerOrgId: ${ticket.partnerOrgId}, user partner: ${partnerPublicUid}`);
      return hasAccess;
    }

    // Partner user can only access their own tickets within their partner (using partnerUserId and partnerOrgId fields)
    if (scopeContext.scope === SupportScope.PARTNER_SUPPORT_USER) {
      const partnerPublicUid = scopeContext.partnerInfo?.public_uid;
      const isOwnTicket = ticket.partnerUserId === scopeContext.userId;
      const isInPartner = ticket.partnerOrgId === partnerPublicUid;
      const hasAccess = isOwnTicket && isInPartner;
      this.logger.log(`${hasAccess ? '✅' : '❌'} [SCOPE FILTER] Partner user access - isOwnTicket: ${isOwnTicket} (${ticket.partnerUserId} === ${scopeContext.userId}), isInPartner: ${isInPartner} (${ticket.partnerOrgId} === ${partnerPublicUid})`);
      return hasAccess;
    }

    // Fallback - deny access for unknown scopes
    this.logger.warn(`❌ [SCOPE FILTER] Unknown scope ${scopeContext.scope} - access denied`);
    return false;
  }

  /**
   * Check if user can modify a specific ticket
   */
  canModifyTicket(scopeContext: ScopeContext, ticket: any): boolean {
    const permissions = getScopePermissions(scopeContext.scope);

    // Check if user can access the ticket first
    if (!this.canAccessTicket(scopeContext, ticket)) {
      return false;
    }

    // Check if user has update permissions
    if (!permissions.canUpdateTickets) {
      return false;
    }

    // Global admin can modify everything
    if (scopeContext.scope === SupportScope.GLOBAL_SUPPORT_ADMIN) {
      return true;
    }

    // Admin users can modify all tickets within their tenant
    if (canViewAllTicketsInTenant(scopeContext.scope)) {
      return true;
    }

    // Regular users can only modify their own tickets
    return ticket.sub === scopeContext.userId;
  }

  /**
   * Check if user can delete a specific ticket
   */
  canDeleteTicket(scopeContext: ScopeContext, ticket: any): boolean {
    const permissions = getScopePermissions(scopeContext.scope);

    // Check if user can access the ticket first
    if (!this.canAccessTicket(scopeContext, ticket)) {
      return false;
    }

    // Check if user has delete permissions
    if (!permissions.canDeleteTickets) {
      return false;
    }

    // Global admin can delete everything
    if (scopeContext.scope === SupportScope.GLOBAL_SUPPORT_ADMIN) {
      return true;
    }

    // Admin users can delete all tickets within their tenant
    if (canViewAllTicketsInTenant(scopeContext.scope)) {
      return true;
    }

    // Regular users cannot delete tickets (handled by permissions check above)
    return false;
  }

  /**
   * Get appropriate data for ticket creation based on scope
   */
  getTicketCreationData(scopeContext: ScopeContext, inputData: any): any {
    const data = { ...inputData };

    // Always set the user ID in sub field for backward compatibility
    data.sub = scopeContext.userId;

    // Set partner information according to requirements
    if (scopeContext.tenantType === TenantType.PARTNER && scopeContext.partnerInfo) {
      // Store partner's public_uid in partnerOrgId field (as per requirements)
      data.partnerOrgId = scopeContext.partnerInfo.public_uid;
      // Store user's ID in partnerUserId field (as per requirements)
      data.partnerUserId = scopeContext.userId;
      // Store user's scope in partnerRole field (as per requirements)
      data.partnerRole = scopeContext.scope;

      this.logger.log(`🏢 [SCOPE FILTER] Setting partner data - partnerOrgId: ${data.partnerOrgId}, partnerUserId: ${data.partnerUserId}, partnerRole: ${data.partnerRole}`);
    } else if (scopeContext.tenantType === TenantType.ACCOUNT && scopeContext.tenantId) {
      data.accountId = scopeContext.tenantId;
      this.logger.log(`🏛️ [SCOPE FILTER] Setting accountId to: ${data.accountId}`);
    }

    this.logger.log(`📝 [SCOPE FILTER] Ticket creation data prepared for user ${scopeContext.userId}:`, data);
    return data;
  }

  /**
   * Log scope-based access decision
   */
  logAccessDecision(scopeContext: ScopeContext, resource: string, action: string, allowed: boolean): void {
    const logLevel = allowed ? 'log' : 'warn';
    const emoji = allowed ? '✅' : '❌';
    
    this.logger[logLevel](
      `${emoji} [SCOPE FILTER] ${action} ${resource} - User: ${scopeContext.userId}, ` +
      `Scope: ${scopeContext.scope}, Tenant: ${scopeContext.tenantType}:${scopeContext.tenantId}, ` +
      `Result: ${allowed ? 'ALLOWED' : 'DENIED'}`
    );
  }
}
