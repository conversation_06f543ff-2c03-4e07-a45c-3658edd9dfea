import { InputType, Field, registerEnumType } from "@nestjs/graphql";
import { ApiProperty } from "@nestjs/swagger";
import { TicketPriority, TicketStatus } from "@prisma/client";
import { IsString, IsEnum, IsArray, IsOptional, IsUUID } from "class-validator";

registerEnumType(TicketPriority, {
  name: "TicketPriority",
  description: "The priority of the ticket"
});
registerEnumType(TicketStatus, {
  name: "TicketStatus",
  description: "The status of the ticket"
});

@InputType()
export class UpdateTicketDto {
  @ApiProperty({ description: "Ticket subject", required: false })
  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  subject?: string;

  @ApiProperty({ description: "Ticket description", required: false })
  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  description?: string;

  @ApiProperty({ description: "Ticket status", enum: TicketStatus, required: false })
  @IsOptional()
  @IsEnum(TicketStatus)
  @Field(() => TicketStatus, { nullable: true })
  status?: TicketStatus;

  @ApiProperty({ description: "Ticket priority", enum: TicketPriority, required: false })
  @IsOptional()
  @IsEnum(TicketPriority)
  @Field(() => TicketPriority, { nullable: true })
  priority?: TicketPriority;

  @ApiProperty({ description: "Category ID", required: false })
  @IsOptional()
  @IsUUID()
  @Field({ nullable: true })
  categoryId?: string;

  @ApiProperty({ description: "Account ID", required: false })
  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  accountId?: string;

  @ApiProperty({ description: "Partner ID", required: false })
  @IsOptional()
  @IsString()
  @Field({ nullable: true })
  partnerId?: string;

  @ApiProperty({ description: "Users to assign the ticket to", required: false, type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Field(() => [String], { nullable: true })
  assignedTo?: string[];
}
