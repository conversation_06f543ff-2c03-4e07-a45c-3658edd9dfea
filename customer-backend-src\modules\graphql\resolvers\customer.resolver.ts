import { Resolver, Query, Mutation, Args, ID, Int } from '@nestjs/graphql';
import { Logger, UseGuards } from '@nestjs/common';
import { Customer, PaginatedCustomers } from '../types/customer.types';
import { CustomerStatus } from '@prisma/client';
import {
  CreateCustomerInput,
  UpdateCustomerInput,
  CustomerFilterInput,
  PaginationInput
} from '../inputs/customer.inputs';
import {
  CustomersQueryService,
  CustomerWithRelations
} from '../../customers-query/customers-query.service';
import { CustomersManagementService } from '../../customers-management/customers-management.service';
import { CustomersVerificationService } from '../../customers-verification/customers-verification.service';
import { CustomersAdminService } from '../../customers-admin/customers-admin.service';
import { GraphQLAuthGuard } from '../guards/graphql-auth.guard';
import { Public } from '../../auth-shared/decorators/public.decorator';

@Resolver(() => Customer)
@UseGuards(GraphQLAuthGuard)
export class CustomerResolver {
  private readonly logger = new Logger(CustomerResolver.name);

  constructor(
    private readonly queryService: CustomersQueryService,
    private readonly managementService: CustomersManagementService,
    private readonly verificationService: CustomersVerificationService,
    private readonly adminService: CustomersAdminService,
  ) {}

  /**
   * Convert CustomerWithRelations to GraphQL Customer type
   */
  private mapToGraphQLCustomer(customer: CustomerWithRelations): Customer {
    const graphqlCustomer = new Customer();

    // Copy all properties
    Object.assign(graphqlCustomer, customer);

    // Handle nullable fields
    graphqlCustomer.externalId = customer.externalId || undefined;

    return graphqlCustomer;
  }

  /**
   * Convert array of CustomerWithRelations to GraphQL Customer array
   */
  private mapToGraphQLCustomerArray(customers: CustomerWithRelations[]): Customer[] {
    return customers.map(customer => this.mapToGraphQLCustomer(customer));
  }

  @Query(() => PaginatedCustomers, { name: 'customers' })
  @Public() // Temporarily public for testing
  async getCustomers(
    @Args('page', { type: () => Int, nullable: true, defaultValue: 1 }) page?: number,
    @Args('limit', { type: () => Int, nullable: true, defaultValue: 10 }) limit?: number,
    @Args('filter', { nullable: true }) filter?: string,
  ): Promise<PaginatedCustomers> {
    this.logger.log(`Getting customers with page: ${page}, limit: ${limit}, filter: ${filter}`);

    const skip = ((page || 1) - 1) * (limit || 10);
    const take = limit || 10;

    // Convert string filter to CustomerFilterInput if provided
    const customerFilter: CustomerFilterInput = filter ? {
      search: filter
    } : {};

    const paginationOptions = {
      skip,
      take,
      orderBy: { createdAt: 'desc' as const }
    };

    const customers = await this.queryService.findMany(customerFilter, paginationOptions);
    const total = await this.queryService.count(customerFilter);

    return {
      data: this.mapToGraphQLCustomerArray(customers),
      total,
      page: page || 1,
      limit: limit || 10
    };
  }

  @Query(() => Customer, { name: 'customer', nullable: true })
  @Public() // Temporarily public for testing
  async getCustomer(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<Customer | null> {
    this.logger.log(`Getting customer ${id}`);

    const customer = await this.queryService.findById(id);
    return customer ? this.mapToGraphQLCustomer(customer) : null;
  }

  @Query(() => Customer, { name: 'customerByEmail', nullable: true })
  async getCustomerByEmail(
    @Args('email') email: string,
  ): Promise<Customer | null> {
    this.logger.log(`Getting customer by email ${email}`);

    const customer = await this.queryService.findByEmail(email);
    return customer ? this.mapToGraphQLCustomer(customer) : null;
  }

  @Query(() => [Customer], { name: 'searchCustomers' })
  async searchCustomers(
    @Args('filter', { nullable: true }) filter?: CustomerFilterInput,
    @Args('pagination', { nullable: true }) pagination?: PaginationInput,
  ): Promise<Customer[]> {
    this.logger.log(`Advanced search`);

    const orderBy = { createdAt: 'desc' as const };

    const paginationOptions = {
      skip: pagination?.skip,
      take: pagination?.take,
      orderBy
    };

    const customers = await this.queryService.findMany(filter, paginationOptions);
    return this.mapToGraphQLCustomerArray(customers);
  }

  @Mutation(() => Customer)
  @Public() // Make public to match REST API behavior
  async createCustomer(
    @Args('input') input: CreateCustomerInput,
  ): Promise<Customer> {
    this.logger.log(`Creating customer`);

    // Convert GraphQL input to Prisma input
    const customerData = {
      ...input,
      dateOfBirth: input.dateOfBirth ? new Date(input.dateOfBirth) : undefined,
    };

    const customer = await this.managementService.create(customerData);
    return this.mapToGraphQLCustomer(customer);
  }

  @Mutation(() => Customer)
  @Public() // Make public to match REST API behavior
  async updateCustomer(
    @Args('input') input: UpdateCustomerInput,
  ): Promise<Customer> {
    this.logger.log(`Updating customer ${input.id}`);

    // Convert GraphQL input to Prisma input
    const { id, ...updateData } = input;
    const customerData = {
      ...updateData,
      dateOfBirth: updateData.dateOfBirth ? new Date(updateData.dateOfBirth) : undefined,
    };

    const customer = await this.managementService.update(id, customerData);
    return this.mapToGraphQLCustomer(customer);
  }

  @Mutation(() => Boolean)
  @Public() // Make public to match REST API behavior
  async deleteCustomer(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<boolean> {
    this.logger.log(`Deleting customer ${id}`);

    await this.managementService.delete(id);
    return true;
  }

  @Mutation(() => Customer)
  @Public() // Make public to match REST API behavior
  async verifyCustomerEmail(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<Customer> {
    this.logger.log(`Verifying email for customer ${id}`);

    const customer = await this.verificationService.verifyEmail(id);
    return this.mapToGraphQLCustomer(customer);
  }

  @Mutation(() => Customer)
  @Public() // Make public to match REST API behavior
  async verifyCustomerPhone(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<Customer> {
    this.logger.log(`Verifying phone for customer ${id}`);

    const customer = await this.verificationService.verifyPhone(id);
    return this.mapToGraphQLCustomer(customer);
  }

  @Query(() => [Customer], { name: 'customersAdmin' })
  async getCustomersAdmin(
    @Args('filter', { nullable: true }) filter?: CustomerFilterInput,
    @Args('pagination', { nullable: true }) pagination?: PaginationInput,
  ): Promise<Customer[]> {
    this.logger.log(`Admin getting customers`);

    const orderBy = pagination?.take ? {
      createdAt: 'desc' as const
    } : { createdAt: 'desc' as const };

    // Admin can see all customers without restrictions
    const customers = await this.queryService.findMany(filter, { ...pagination, orderBy });
    return this.mapToGraphQLCustomerArray(customers);
  }

  @Mutation(() => Customer)
  @Public() // Make public to match REST API behavior
  async updateCustomerStatus(
    @Args('id', { type: () => ID }) id: string,
    @Args('status') status: string,
  ): Promise<Customer> {
    this.logger.log(`Admin updating customer ${id} status to ${status}`);

    const customer = await this.adminService.adminUpdateCustomer(id, { status: status as CustomerStatus });
    return this.mapToGraphQLCustomer(customer);
  }
}
