import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '../../../config/config.service';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import * as crypto from 'crypto';
import * as jwt from 'jsonwebtoken';
// JWKS client temporarily disabled due to import issues
// const JwksClient = require('jwks-client');
import { User, JWTPayload, AuthConfig, DecryptedTokens } from './types/auth.types';

@Injectable()
export class AuthSharedService {
  private readonly logger = new Logger(AuthSharedService.name);
  private readonly config: AuthConfig;
  private jwksClient: any; // JwksClient type not available

  constructor(
    private configService: ConfigService,
    private httpService: HttpService
  ) {
    this.config = {
      authJwksUrl: this.configService.get('AUTH_JWKS_URL') || 'https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks',
      encryptionKey: this.configService.get('ACCESS_TOKEN_ENCRYPTION_KEY') || 'b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8',
      cookieNames: {
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
      },
      jwt: {
        issuer: process.env.JWT_ISSUER_URL || 'https://ng-auth-dev.dev1.ngnair.com',
        audience: process.env.JWT_AUDIENCE || 'ngnair',
        skipVerification: process.env.JWT_SKIP_VERIFICATION === 'true',
      },
    };

    // Initialize JWKS client for JWT verification (temporarily disabled)
    // TODO: Fix JWKS client import issues
    this.jwksClient = null;
    this.logger.warn('⚠️ [AUTH SERVICE] JWKS client temporarily disabled - JWT signature verification will be skipped');
  }

  /**
   * Decrypt cookies using Rails-compatible AES-256-GCM encryption
   * Following the exact sequence: URL decode -> Base64 decode -> Rails MessageEncryptor -> AES-256-GCM decrypt
   */
  private async decryptCookie(encryptedValue: string): Promise<string> {
    try {
      this.logger.log(`🔓 [AUTH SERVICE] Starting Rails-compatible decryption process...`);
      this.logger.log(`🔐 [AUTH SERVICE] Raw encrypted value length: ${encryptedValue.length}`);
      this.logger.log(`🔐 [AUTH SERVICE] Raw encrypted value (first 100 chars): ${encryptedValue.substring(0, 100)}...`);

      // STEP 1: URL decode the cookie value (it may be URL-encoded)
      this.logger.log(`📋 [AUTH SERVICE] Step 1: URL decoding cookie value...`);
      let urlDecodedValue: string;
      try {
        urlDecodedValue = decodeURIComponent(encryptedValue);
        this.logger.log(`✅ [AUTH SERVICE] URL decoded successfully, length: ${urlDecodedValue.length}`);
        this.logger.log(`🔐 [AUTH SERVICE] URL decoded value (first 100 chars): ${urlDecodedValue.substring(0, 100)}...`);
      } catch (urlError) {
        this.logger.log('⚠️ [AUTH SERVICE] URL decoding failed, using original value');
        urlDecodedValue = encryptedValue;
      }

      // STEP 2: Base64 decode the URL-decoded value to get Rails MessageEncryptor format
      this.logger.log(`📋 [AUTH SERVICE] Step 2: Base64 decoding to get Rails MessageEncryptor format...`);
      let base64DecodedValue: string;
      try {
        // Handle URL-safe base64 if needed
        const standardBase64 = urlDecodedValue.replace(/-/g, '+').replace(/_/g, '/');
        // Add padding if needed
        const paddedBase64 = standardBase64 + '='.repeat((4 - standardBase64.length % 4) % 4);
        base64DecodedValue = Buffer.from(paddedBase64, 'base64').toString('utf8');
        this.logger.log(`✅ [AUTH SERVICE] Base64 decoded successfully, length: ${base64DecodedValue.length}`);
        this.logger.log(`🔐 [AUTH SERVICE] Base64 decoded value (first 100 chars): ${base64DecodedValue.substring(0, 100)}...`);
      } catch (base64Error) {
        this.logger.error(`❌ [AUTH SERVICE] Base64 decoding failed: ${base64Error.message}`);
        throw new Error('Failed to base64 decode token');
      }

      // STEP 3: Parse Rails MessageEncryptor format: encrypted_data--iv--auth_tag
      this.logger.log(`📋 [AUTH SERVICE] Step 3: Parsing Rails MessageEncryptor format...`);
      const parts = base64DecodedValue.split('--');
      this.logger.log(`🔐 [AUTH SERVICE] Split into ${parts.length} parts using Rails format (--)`);

      if (parts.length !== 3) {
        this.logger.error(`❌ [AUTH SERVICE] Invalid Rails MessageEncryptor format. Expected 3 parts, got ${parts.length}`);
        this.logger.error(`🔐 [AUTH SERVICE] First 100 chars of base64 decoded value: ${base64DecodedValue.substring(0, 100)}`);
        throw new Error('Invalid Rails MessageEncryptor format - expected encrypted_data--iv--auth_tag');
      }

      // STEP 4: Extract encrypted data, IV, and auth tag (all base64 encoded)
      this.logger.log(`📋 [AUTH SERVICE] Step 4: Extracting encrypted components...`);
      const encryptedData = Buffer.from(parts[0], 'base64');
      const iv = Buffer.from(parts[1], 'base64');
      const authTag = Buffer.from(parts[2], 'base64');

      this.logger.log(`🔐 [AUTH SERVICE] Encrypted data length: ${encryptedData.length}, IV length: ${iv.length}, Auth tag length: ${authTag.length}`);

      // STEP 5: Decrypt using AES-256-GCM with the configured encryption key
      this.logger.log(`📋 [AUTH SERVICE] Step 5: Decrypting using AES-256-GCM...`);
      const key = Buffer.from(this.config.encryptionKey, 'hex');
      this.logger.log(`🔑 [AUTH SERVICE] Using encryption key length: ${key.length} bytes`);

      // Try primary decryption method
      let decrypted: string;
      try {
        // Create decipher for GCM mode
        const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv);
        decipher.setAuthTag(authTag);

        // Decrypt to get the JWT token string
        decrypted = decipher.update(encryptedData, undefined, 'utf8');
        decrypted += decipher.final('utf8');

        this.logger.log(`✅ [AUTH SERVICE] Primary decryption method successful`);
      } catch (primaryError) {
        this.logger.warn(`⚠️ [AUTH SERVICE] Primary decryption failed: ${primaryError.message}`);
        this.logger.log(`🔄 [AUTH SERVICE] Trying alternative decryption method...`);

        // Try alternative method with different key encoding
        try {
          const alternativeKey = Buffer.from(this.config.encryptionKey, 'utf8');
          const altDecipher = crypto.createDecipheriv('aes-256-gcm', alternativeKey, iv);
          altDecipher.setAuthTag(authTag);

          decrypted = altDecipher.update(encryptedData, undefined, 'utf8');
          decrypted += altDecipher.final('utf8');

          this.logger.log(`✅ [AUTH SERVICE] Alternative decryption method successful`);
        } catch (alternativeError) {
          this.logger.error(`❌ [AUTH SERVICE] Both decryption methods failed`);
          this.logger.error(`Primary error: ${primaryError.message}`);
          this.logger.error(`Alternative error: ${alternativeError.message}`);
          throw primaryError; // Throw the original error
        }
      }

      this.logger.log(`✅ [AUTH SERVICE] AES-256-GCM decryption successful, result length: ${decrypted.length}`);

      // Remove quotes if present (Rails adds quotes around JSON strings)
      const jwtToken = decrypted.replace(/^"(.*)"$/, '$1');
      this.logger.log(`✅ [AUTH SERVICE] JWT token extracted, length: ${jwtToken.length}`);
      this.logger.log(`🎯 [AUTH SERVICE] JWT token (first 100 chars): ${jwtToken.substring(0, 100)}...`);

      return jwtToken;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Decryption failed: ${error.message}`);
      this.logger.error(`❌ [AUTH SERVICE] Error stack: ${error.stack}`);
      throw new UnauthorizedException('Failed to decrypt authentication token');
    }
  }

  /**
   * Extract raw encrypted tokens from cookies (without decryption)
   */
  extractRawTokensFromCookies(cookies: Record<string, string>): { accessToken?: string; refreshToken?: string } {
    const encryptedAccessToken = cookies[this.config.cookieNames.accessToken];
    const encryptedRefreshToken = cookies[this.config.cookieNames.refreshToken];

    this.logger.log(`Available cookies: ${Object.keys(cookies)}`);
    this.logger.log(`Looking for access token: ${this.config.cookieNames.accessToken}`);
    this.logger.log(`Looking for refresh token: ${this.config.cookieNames.refreshToken}`);
    this.logger.log(`Access token found: ${!!encryptedAccessToken}`);
    this.logger.log(`Refresh token found: ${!!encryptedRefreshToken}`);

    return {
      accessToken: encryptedAccessToken,
      refreshToken: encryptedRefreshToken,
    };
  }

  /**
   * Extract and decrypt tokens from cookies
   */
  async extractTokensFromCookies(cookies: Record<string, string>): Promise<DecryptedTokens> {
    const encryptedAccessToken = cookies[this.config.cookieNames.accessToken];
    const encryptedRefreshToken = cookies[this.config.cookieNames.refreshToken];

    this.logger.log(`Available cookies: ${Object.keys(cookies)}`);
    this.logger.log(`Looking for access token: ${this.config.cookieNames.accessToken}`);
    this.logger.log(`Looking for refresh token: ${this.config.cookieNames.refreshToken}`);
    this.logger.log(`Access token found: ${!!encryptedAccessToken}`);
    this.logger.log(`Refresh token found: ${!!encryptedRefreshToken}`);

    if (!encryptedAccessToken) {
      throw new UnauthorizedException('Missing access token');
    }

    if (!encryptedRefreshToken) {
      this.logger.warn('Refresh token not found, proceeding with access token only');
    }

    try {
      const accessToken = await this.decryptCookie(encryptedAccessToken);
      const refreshToken = encryptedRefreshToken ? await this.decryptCookie(encryptedRefreshToken) : undefined;

      return { accessToken, refreshToken };
    } catch (error) {
      this.logger.error('Failed to decrypt tokens:', error);
      throw new UnauthorizedException('Invalid authentication tokens');
    }
  }

  /**
   * Verify JWT token using JWKS endpoint
   * Step 3: JWT Verification and Decoding
   */
  async verifyToken(token: string): Promise<JWTPayload> {
    try {
      this.logger.log(`🔍 [AUTH SERVICE] Starting JWT verification using JWKS...`);
      this.logger.log(`🎫 [AUTH SERVICE] JWT token length: ${token.length}`);

      // Decode token header to get kid (key ID)
      const decoded = jwt.decode(token, { complete: true });

      if (!decoded || !decoded.header) {
        throw new UnauthorizedException('Invalid JWT token format');
      }

      const header = decoded.header;
      this.logger.log(`📋 [AUTH SERVICE] JWT Header: ${JSON.stringify(header)}`);

      // Get the key ID from the header
      const kid = header.kid || 'default';
      this.logger.log(`🔑 [AUTH SERVICE] Using key ID: ${kid}`);

      // Verify JWT signature using JWKS if available
      if (this.jwksClient && this.config.authJwksUrl) {
        this.logger.log(`🔍 [AUTH SERVICE] Fetching public key from JWKS endpoint: ${this.config.authJwksUrl}`);

        try {
          const key = await this.jwksClient.getSigningKey(kid);
          const signingKey = key.getPublicKey();

          this.logger.log(`✅ [AUTH SERVICE] Retrieved public key for kid: ${kid}`);

          // Verify the JWT signature
          const verifiedPayload = jwt.verify(token, signingKey, {
            algorithms: ['RS256', 'RS384', 'RS512'],
            issuer: this.config.jwt?.issuer,
            audience: this.config.jwt?.audience,
          }) as JWTPayload;

          this.logger.log(`✅ [AUTH SERVICE] JWT signature verified successfully`);
          return verifiedPayload;

        } catch (jwksError) {
          this.logger.error(`❌ [AUTH SERVICE] JWKS verification failed: ${jwksError.message}`);

          // Fall back to payload extraction without signature verification in development
          if (this.config.jwt?.skipVerification) {
            this.logger.warn('⚠️ [AUTH SERVICE] Falling back to payload extraction without signature verification (development mode)');
          } else {
            throw new UnauthorizedException('JWT signature verification failed');
          }
        }
      } else {
        this.logger.warn('⚠️ [AUTH SERVICE] JWKS client not configured - skipping signature verification');
      }

      // Extract payload without signature verification (development mode or fallback)
      const payload = decoded.payload as JWTPayload;
      this.logger.log(`📋 [AUTH SERVICE] JWT Payload extracted: ${JSON.stringify(payload, null, 2)}`);

      // Validate required fields based on Ruby JWT structure
      if (!payload.sub) {
        throw new UnauthorizedException('Invalid JWT payload - missing required field: sub');
      }

      return payload;
    } catch (error) {
      this.logger.error('Token verification failed:', error);
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

  /**
   * Get user from JWT payload - returns ONLY actual JWT fields (no computed/hardcoded fields)
   */
  getUserFromPayload(payload: JWTPayload): any {
    // Validate required JWT fields based on actual Ruby payload structure
    if (!payload.sub || !payload.sid || !payload.ent_set || !payload.jti || !payload.azp) {
      this.logger.error(`❌ [AUTH SERVICE] Missing required JWT fields. Payload: ${JSON.stringify(payload)}`);
      throw new UnauthorizedException('Invalid JWT payload - missing required fields');
    }

    this.logger.log(`📋 [AUTH SERVICE] Processing JWT for user: ${payload.sub}`);
    this.logger.log(`📋 [AUTH SERVICE] Entity set: ${payload.ent_set}`);
    this.logger.log(`📋 [AUTH SERVICE] Email present: ${!!payload.email}`);
    this.logger.log(`📋 [AUTH SERVICE] Session ID: ${payload.sid}`);
    this.logger.log(`📋 [AUTH SERVICE] Returning ONLY actual JWT payload fields (no computed fields)`);

    // Return ONLY the actual fields from the JWT payload - no computed, derived, or hardcoded fields
    const actualJwtFields: any = {
      // Standard JWT fields (exactly as they appear in the JWT)
      iss: payload.iss,
      sub: payload.sub,
      aud: payload.aud,
      exp: payload.exp,
      iat: payload.iat,
      jti: payload.jti,

      // NGNair-specific fields (exactly as they appear in the JWT)
      sid: payload.sid,
      azp: payload.azp,
      ent_set: payload.ent_set,
      perm_v: payload.perm_v,
      amr: payload.amr,
      auth_time: payload.auth_time,
    };

    // Only include email if it actually exists in the JWT
    if (payload.email !== undefined) {
      actualJwtFields.email = payload.email;
    }

    this.logger.log(`📋 [AUTH SERVICE] Actual JWT fields being returned: ${JSON.stringify(Object.keys(actualJwtFields))}`);

    return actualJwtFields;
  }



  /**
   * Authenticate user from cookies
   */
  async authenticateFromCookies(cookies: Record<string, string>): Promise<User> {
    this.logger.log('🔐 [AUTH SERVICE] Starting authentication from cookies');
    this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    try {
      // Extract and decrypt tokens
      this.logger.log('🔓 [AUTH SERVICE] Extracting and decrypting tokens...');
      const { accessToken } = await this.extractTokensFromCookies(cookies);
      this.logger.log(`🔑 [AUTH SERVICE] Access token extracted: ${accessToken ? 'YES' : 'NO'}`);

      // Verify access token
      this.logger.log('✅ [AUTH SERVICE] Verifying access token...');
      const payload = await this.verifyToken(accessToken);
      this.logger.log(`👤 [AUTH SERVICE] Token verified, user ID: ${payload.sub}`);

      // Return user info
      this.logger.log('📋 [AUTH SERVICE] Getting user info from payload...');
      const user = this.getUserFromPayload(payload);
      this.logger.log(`✅ [AUTH SERVICE] Authentication successful: ${user.email} (${user.role})`);

      return user;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Authentication failed: ${error.message}`);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  /**
   * Check if user has required permissions
   */
  hasPermissions(user: User, requiredPermissions: string[]): boolean {
    if (!user.permissions) return false;
    return requiredPermissions.every(permission => user.permissions!.includes(permission));
  }

  /**
   * Check if user has required role
   */
  hasRole(user: User, requiredRole: string): boolean {
    return user.role === requiredRole;
  }

  /**
   * Authenticate user from cookies using proper external auth service integration
   * This is the main method for the /auth/me endpoint
   * Returns the actual JWT payload fields only (no computed/hardcoded fields)
   */
  async authenticateUserFromCookies(cookies: Record<string, string>): Promise<any> {
    this.logger.log('🔐 [AUTH SERVICE] Starting authentication for /auth/me endpoint');
    this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    try {
      // Extract encrypted access token from cookies
      const encryptedAccessToken = cookies[this.config.cookieNames.accessToken];

      if (!encryptedAccessToken) {
        throw new UnauthorizedException('Missing access token');
      }

      this.logger.log(`🔑 [AUTH SERVICE] Found encrypted access token: ${encryptedAccessToken.substring(0, 50)}...`);

      // Step 1: Decrypt the access token
      this.logger.log('🔓 [AUTH SERVICE] Step 1: Decrypting access token...');
      const decryptedToken = await this.decryptCookie(encryptedAccessToken);
      this.logger.log(`✅ [AUTH SERVICE] Token decrypted successfully, length: ${decryptedToken.length}`);

      // Step 2: Verify JWT using JWKS
      this.logger.log('🔍 [AUTH SERVICE] Step 2: Verifying JWT with JWKS...');
      const payload = await this.verifyToken(decryptedToken);
      this.logger.log(`✅ [AUTH SERVICE] JWT verified successfully for user: ${payload.sub}`);

      // Step 3: Extract user information from JWT payload
      this.logger.log('👤 [AUTH SERVICE] Step 3: Extracting user information...');
      const user = this.getUserFromPayload(payload);
      this.logger.log(`✅ [AUTH SERVICE] User authenticated successfully: ${user.email} (${user.role})`);

      return user;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Authentication failed: ${error.message}`);
      this.logger.error(`❌ [AUTH SERVICE] Error stack: ${error.stack}`);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  /**
   * Get user by ID from external auth service
   */
  async getUserById(userId: string, cookies: Record<string, string>): Promise<User> {
    this.logger.log(`🔍 [AUTH SERVICE] Getting user by ID: ${userId}`);
    this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    try {
      // Extract raw encrypted access token from cookies
      const { accessToken: encryptedAccessToken } = this.extractRawTokensFromCookies(cookies);

      if (!encryptedAccessToken) {
        throw new UnauthorizedException('Missing access token');
      }

      // Get auth service URL from config
      const authServiceUrl = this.configService.get('AUTH_SERVICE_URL') || 'https://ng-auth-dev.dev1.ngnair.com';
      const url = `${authServiceUrl}/api/v1/users/${userId}`;

      this.logger.log(`🌐 [AUTH SERVICE] Making request to: ${url}`);
      this.logger.log(`🍪 [AUTH SERVICE] Forwarding cookie: access_token=${encryptedAccessToken.substring(0, 50)}...`);

      // Prepare headers for external service request
      const requestHeaders = {
        'Cookie': `access_token=${encryptedAccessToken}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Support-Service/1.0',
      };

      // Make request to external auth service
      const response = await firstValueFrom(
        this.httpService.get(url, {
          headers: requestHeaders,
          timeout: 10000,
        })
      );

      this.logger.log(`✅ [AUTH SERVICE] User retrieved from external service`);
      this.logger.log(`📄 [AUTH SERVICE] Response data: ${JSON.stringify(response.data)}`);

      // Return the user data from the response
      return response.data;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Failed to get user by ID from external service: ${error.message}`);
      if (error.response?.status === 401) {
        throw new UnauthorizedException('Invalid or expired access token');
      }
      if (error.response?.status === 404) {
        throw new UnauthorizedException('User not found');
      }
      throw new UnauthorizedException('Failed to retrieve user information');
    }
  }

  /**
   * Get all users from external auth service
   */
  async getAllUsers(cookies: Record<string, string>): Promise<User[]> {
    this.logger.log('🔍 [AUTH SERVICE] Getting all users from external service');
    this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    try {
      // Extract raw encrypted access token from cookies
      const { accessToken: encryptedAccessToken } = this.extractRawTokensFromCookies(cookies);

      if (!encryptedAccessToken) {
        throw new UnauthorizedException('Missing access token');
      }

      // Get auth service URL from config
      const authServiceUrl = this.configService.get('AUTH_SERVICE_URL') || 'https://ng-auth-dev.dev1.ngnair.com';
      const url = `${authServiceUrl}/api/v1/users`;

      this.logger.log(`🌐 [AUTH SERVICE] Making request to: ${url}`);
      this.logger.log(`🍪 [AUTH SERVICE] Forwarding cookie: access_token=${encryptedAccessToken.substring(0, 50)}...`);

      // Prepare headers for external service request
      const requestHeaders = {
        'Cookie': `access_token=${encryptedAccessToken}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Support-Service/1.0',
      };

      // Make request to external auth service
      const response = await firstValueFrom(
        this.httpService.get(url, {
          headers: requestHeaders,
          timeout: 10000,
        })
      );

      this.logger.log(`✅ [AUTH SERVICE] Users retrieved from external service`);
      this.logger.log(`📄 [AUTH SERVICE] Response data: ${JSON.stringify(response.data)}`);

      // Return the users array from the response
      const usersData = Array.isArray(response.data) ? response.data : [response.data];
      return usersData;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Failed to get all users from external service: ${error.message}`);
      if (error.response?.status === 401) {
        throw new UnauthorizedException('Invalid or expired access token');
      }
      throw new UnauthorizedException('Failed to retrieve users information');
    }
  }
}
