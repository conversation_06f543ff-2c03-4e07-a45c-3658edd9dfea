/**
 * Advanced Integration Example
 * 
 * This example shows advanced usage patterns including:
 * - Custom configuration with ConfigService
 * - Role-based access control
 * - Custom decorators
 * - Error handling
 * - Swagger documentation
 */

import { Module, Injectable, SetMetadata, createParamDecorator, ExecutionContext } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Controller, Get, UseGuards, Req, ForbiddenException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Reflector } from '@nestjs/core';
import { 
  AuthModule, 
  AuthGuard, 
  AuthService,
  User,
  AuthConfig
} from '@ngnair/auth-integration-package';

// =============================================================================
// CUSTOM DECORATORS
// =============================================================================

// Role-based access control decorator
export const Roles = (...roles: string[]) => SetMetadata('roles', roles);

// Current user decorator
export const CurrentUser = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): User => {
    const request = ctx.switchToHttp().getRequest();
    return request.user;
  },
);

// =============================================================================
// CUSTOM GUARDS
// =============================================================================

@Injectable()
export class RolesGuard {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>('roles', [
      context.getHandler(),
      context.getClass(),
    ]);
    
    if (!requiredRoles) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
    
    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    const hasRole = requiredRoles.some((role) => user.role === role);
    
    if (!hasRole) {
      throw new ForbiddenException(`User does not have required role. Required: ${requiredRoles.join(', ')}, User has: ${user.role}`);
    }

    return true;
  }
}

// =============================================================================
// CUSTOM SERVICE
// =============================================================================

@Injectable()
export class UserService {
  constructor(private readonly authService: AuthService) {}

  async getCurrentUserProfile(cookies: Record<string, string>): Promise<any> {
    try {
      // Get user from local JWT processing
      const user = await this.authService.authenticateUserFromCookies(cookies);
      
      // Add additional profile information
      return {
        ...user,
        profileComplete: !!(user.firstName && user.lastName),
        lastLogin: new Date().toISOString(),
        preferences: {
          theme: 'light',
          language: 'en',
          notifications: true,
        },
      };
    } catch (error) {
      throw error;
    }
  }

  async getUserStats(cookies: Record<string, string>): Promise<any> {
    try {
      // This would typically fetch from external service
      const users = await this.authService.getAllUsers(cookies);
      
      return {
        totalUsers: Array.isArray(users) ? users.length : 0,
        activeUsers: Array.isArray(users) ? users.filter(u => u.active).length : 0,
        lastUpdated: new Date().toISOString(),
      };
    } catch (error) {
      // Handle external service errors gracefully
      return {
        totalUsers: 0,
        activeUsers: 0,
        lastUpdated: new Date().toISOString(),
        error: 'Unable to fetch user statistics',
      };
    }
  }
}

// =============================================================================
// CONTROLLERS
// =============================================================================

@ApiTags('User Management')
@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @ApiOperation({ summary: 'Get current user profile with additional data' })
  @ApiResponse({ status: 200, description: 'User profile retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Authentication required' })
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('profile')
  async getProfile(@Req() request: any) {
    return this.userService.getCurrentUserProfile(request.cookies);
  }

  @ApiOperation({ summary: 'Get user statistics (admin only)' })
  @ApiResponse({ status: 200, description: 'User statistics retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Authentication required' })
  @ApiResponse({ status: 403, description: 'Admin role required' })
  @ApiBearerAuth()
  @UseGuards(AuthGuard, RolesGuard)
  @Roles('admin')
  @Get('stats')
  async getUserStats(@Req() request: any) {
    return this.userService.getUserStats(request.cookies);
  }

  @ApiOperation({ summary: 'Get current user information using decorator' })
  @ApiResponse({ status: 200, description: 'User information retrieved successfully' })
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Get('me')
  async getCurrentUser(@CurrentUser() user: User) {
    return {
      user,
      timestamp: new Date().toISOString(),
      source: 'decorator',
    };
  }
}

@ApiTags('Admin')
@Controller('admin')
@UseGuards(AuthGuard, RolesGuard)
@Roles('admin')
export class AdminController {
  constructor(private readonly authService: AuthService) {}

  @ApiOperation({ summary: 'Admin dashboard data' })
  @ApiResponse({ status: 200, description: 'Dashboard data retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Admin role required' })
  @ApiBearerAuth()
  @Get('dashboard')
  async getDashboard(@Req() request: any, @CurrentUser() user: User) {
    try {
      const users = await this.authService.getAllUsers(request.cookies);
      
      return {
        admin: {
          id: user.id,
          email: user.email,
          role: user.role,
        },
        stats: {
          totalUsers: Array.isArray(users) ? users.length : 0,
          timestamp: new Date().toISOString(),
        },
        systemInfo: {
          version: '1.0.0',
          environment: process.env.NODE_ENV || 'development',
          uptime: process.uptime(),
        },
      };
    } catch (error) {
      return {
        admin: {
          id: user.id,
          email: user.email,
          role: user.role,
        },
        stats: {
          totalUsers: 0,
          timestamp: new Date().toISOString(),
          error: 'Unable to fetch external data',
        },
        systemInfo: {
          version: '1.0.0',
          environment: process.env.NODE_ENV || 'development',
          uptime: process.uptime(),
        },
      };
    }
  }
}

// =============================================================================
// CONFIGURATION
// =============================================================================

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    
    // Advanced auth module configuration with ConfigService
    AuthModule.registerAsync({
      useFactory: (configService: ConfigService): AuthConfig => {
        const config: AuthConfig = {
          encryptionKey: configService.get<string>('ACCESS_TOKEN_ENCRYPTION_KEY'),
          externalAuthServiceUrl: configService.get<string>('EXTERNAL_AUTH_SERVICE_URL'),
          jwksUri: configService.get<string>('JWKS_URI'),
          cookieNames: {
            accessToken: configService.get<string>('ACCESS_TOKEN_COOKIE_NAME', 'access_token'),
            refreshToken: configService.get<string>('REFRESH_TOKEN_COOKIE_NAME', 'refresh_token'),
          },
          requestTimeout: configService.get<number>('AUTH_REQUEST_TIMEOUT', 10000),
          enableDebugLogging: configService.get<boolean>('AUTH_DEBUG_LOGGING', false),
          jwt: {
            issuer: configService.get<string>('JWT_ISSUER'),
            audience: configService.get<string>('JWT_AUDIENCE'),
            skipVerification: configService.get<boolean>('JWT_SKIP_VERIFICATION', false),
          },
        };

        // Validate required configuration
        if (!config.encryptionKey) {
          throw new Error('ACCESS_TOKEN_ENCRYPTION_KEY is required');
        }

        if (!config.externalAuthServiceUrl) {
          throw new Error('EXTERNAL_AUTH_SERVICE_URL is required');
        }

        return config;
      },
      inject: [ConfigService],
    }),
  ],
  controllers: [UserController, AdminController],
  providers: [UserService, RolesGuard],
})
export class AdvancedAppModule {}

// =============================================================================
// USAGE EXAMPLES
// =============================================================================

/*
1. Environment Variables (.env):
ACCESS_TOKEN_ENCRYPTION_KEY=your-64-char-hex-key
EXTERNAL_AUTH_SERVICE_URL=https://auth.example.com/api
JWKS_URI=https://auth.example.com/.well-known/jwks.json
JWT_ISSUER=https://auth.example.com
JWT_AUDIENCE=your-app
NODE_ENV=production

2. Test Role-Based Access:
# This will work for admin users
curl -H "Cookie: access_token=admin-token" http://localhost:3000/admin/dashboard

# This will return 403 for non-admin users
curl -H "Cookie: access_token=user-token" http://localhost:3000/admin/dashboard

3. Test Custom Decorators:
curl -H "Cookie: access_token=valid-token" http://localhost:3000/users/me

4. Test Error Handling:
# External service unavailable - graceful degradation
curl -H "Cookie: access_token=valid-token" http://localhost:3000/users/stats
*/
