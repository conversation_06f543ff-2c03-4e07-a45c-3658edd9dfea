# Staging Deployment Guide - Authentication & CORS Fixes

## Issues Fixed

### 1. CORS Configuration
- **Problem**: Staging domain `https://ng-support-dev.dev1.ngnair.com` was not included in allowed origins
- **Solution**: Added staging-specific CORS configuration with proper HTTPS domains

### 2. Cookie Security Settings
- **Problem**: Cookies were configured for development (`secure: false`) but staging uses HTTPS
- **Solution**: Environment-aware cookie configuration with `secure: true` and `sameSite: 'none'` for staging

### 3. Environment Configuration
- **Problem**: Docker deployment was using `NODE_ENV=development` instead of staging-specific config
- **Solution**: Created `.env.staging` and `docker-compose.staging.yml` with proper staging settings

## Deployment Steps

### Option 1: Using Staging Docker Compose (Recommended)
```bash
# Stop existing containers
docker-compose down

# Build and deploy with staging configuration
docker-compose -f docker-compose.staging.yml build
docker-compose -f docker-compose.staging.yml up -d

# Check logs
docker-compose -f docker-compose.staging.yml logs -f support-backend-api
```

### Option 2: Update Existing Docker Compose
```bash
# Stop existing containers
docker-compose down

# Build and deploy with updated configuration
docker-compose build
docker-compose up -d

# Check logs
docker-compose logs -f support-backend-api
```

## Verification Steps

### 1. Check CORS Configuration
Test that CORS is working for the staging domain:
```bash
curl -H "Origin: https://ng-support-dev.dev1.ngnair.com" \
     -H "Access-Control-Request-Method: GET" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS \
     https://ng-support-dev.dev1.ngnair.com/api/health
```

Expected response should include:
- `Access-Control-Allow-Origin: https://ng-support-dev.dev1.ngnair.com`
- `Access-Control-Allow-Credentials: true`

### 2. Test Authentication Endpoints
```bash
# Test health endpoint
curl https://ng-support-dev.dev1.ngnair.com/api/health

# Test auth/me endpoint (should return 401 without cookies)
curl https://ng-support-dev.dev1.ngnair.com/auth/me

# Test auth/users endpoint (should return 401 without cookies)
curl https://ng-support-dev.dev1.ngnair.com/auth/users
```

### 3. Check Application Logs
Look for these log messages indicating successful configuration:
```
CORS check for origin: https://ng-support-dev.dev1.ngnair.com
CORS allowed origin: https://ng-support-dev.dev1.ngnair.com
🎭 Environment: STAGING
```

### 4. Test Cookie Authentication
With valid cookies from the auth service, test:
```bash
# Replace with actual cookies from browser dev tools
curl -H "Cookie: access_token=<encrypted_token>; refresh_token=<encrypted_token>" \
     https://ng-support-dev.dev1.ngnair.com/auth/me
```

## Configuration Changes Summary

### 1. CORS Origins Updated
- Added `https://ng-support-dev.dev1.ngnair.com`
- Added `https://ng-support-fe-dev.dev1.ngnair.com`
- Added `https://ng-support-admin-dev.dev1.ngnair.com`

### 2. Cookie Settings
- `secure: true` for staging/production
- `sameSite: 'none'` for cross-domain requests in secure environments

### 3. Environment Variables
- `NODE_ENV=staging`
- Updated all URLs to use HTTPS staging domains
- Added proper cookie secret for staging

## Troubleshooting

### If CORS errors persist:
1. Check browser dev tools Network tab for the exact origin being sent
2. Verify the origin matches exactly (including protocol and port)
3. Check application logs for CORS debug messages

### If cookies are not being received:
1. Verify cookies are being set by the auth service with correct domain
2. Check that cookies have `Secure` flag set for HTTPS
3. Verify `SameSite=None` is set for cross-domain requests

### If authentication still fails:
1. Check that `ACCESS_TOKEN_ENCRYPTION_KEY` matches the auth service
2. Verify JWT verification is working (check JWKS endpoint)
3. Test cookie decryption manually using the auth service debug endpoints

## Rollback Plan

If issues occur, rollback to previous configuration:
```bash
# Stop staging containers
docker-compose -f docker-compose.staging.yml down

# Revert to original configuration
git checkout HEAD~1 -- support-backend/src/main.ts
git checkout HEAD~1 -- support-backend/docker-compose.yml

# Rebuild and deploy
docker-compose build
docker-compose up -d
```
