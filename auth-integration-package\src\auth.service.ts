import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import * as crypto from 'crypto';
// JWKS client temporarily disabled due to import issues
// const JwksClient = require('jwks-client');
import { AuthConfig } from './types/auth-config.interface';
import { User, JWTPayload, DecryptedTokens } from './types/auth.types';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private jwksClient: any; // JwksClient type not available

  constructor(
    private readonly httpService: HttpService,
    private readonly config: AuthConfig,
  ) {
    // Initialize JWKS client for JWT verification (temporarily disabled)
    // TODO: Fix JWKS client import issues
    this.jwksClient = null;
    this.logger.warn('⚠️ [AUTH SERVICE] JWKS client temporarily disabled - JWT signature verification will be skipped');

    // Log configuration for debugging
    this.logger.log(`🔧 [AUTH SERVICE] Configuration loaded:`);
    this.logger.log(`🔧 [AUTH SERVICE] - External auth service: ${this.config.externalAuthServiceUrl}`);
    this.logger.log(`🔧 [AUTH SERVICE] - JWKS URI: ${this.config.jwksUri || 'Not configured'}`);
    this.logger.log(`🔧 [AUTH SERVICE] - Access token cookie: ${this.config.cookieNames.accessToken}`);
    this.logger.log(`🔧 [AUTH SERVICE] - Refresh token cookie: ${this.config.cookieNames.refreshToken}`);
    this.logger.log(`🔧 [AUTH SERVICE] - JWT issuer: ${this.config.jwt?.issuer || 'Not configured'}`);
    this.logger.log(`🔧 [AUTH SERVICE] - JWT audience: ${this.config.jwt?.audience || 'Not configured'}`);
    this.logger.log(`🔧 [AUTH SERVICE] - JWT skip verification: ${this.config.jwt?.skipVerification || false}`);
  }

  /**
   * Decrypt cookies using Rails-compatible AES-256-GCM encryption
   * Follows the exact 4-step sequence: URL decode -> Base64 decode -> Parse Rails format -> AES-256-GCM decrypt
   */
  private async decryptCookie(encryptedValue: string): Promise<string> {
    try {
      this.logger.log(`🔓 [AUTH SERVICE] Starting Rails-compatible decryption process...`);
      this.logger.log(`🔐 [AUTH SERVICE] Encrypted value length: ${encryptedValue.length}`);
      this.logger.log(`🔑 [AUTH SERVICE] Using encryption key: ${this.config.encryptionKey.substring(0, 16)}...`);

      // Step 1: URL decode the cookie value
      this.logger.log(`🔐 [AUTH SERVICE] Step 1: URL decoding...`);
      const urlDecoded = decodeURIComponent(encryptedValue);
      this.logger.log(`✅ [AUTH SERVICE] URL decoded successfully, length: ${urlDecoded.length}`);

      // Step 2: Base64 decode to get Rails MessageEncryptor format
      this.logger.log(`🔐 [AUTH SERVICE] Step 2: Base64 decoding...`);
      const base64Decoded = Buffer.from(urlDecoded, 'base64');
      this.logger.log(`✅ [AUTH SERVICE] Base64 decoded successfully, length: ${base64Decoded.length}`);

      // Step 3: Parse Rails MessageEncryptor format: encrypted_data--iv--auth_tag
      this.logger.log(`🔐 [AUTH SERVICE] Step 3: Parsing Rails MessageEncryptor format...`);
      const base64String = base64Decoded.toString('utf8');
      const parts = base64String.split('--');
      this.logger.log(`🔐 [AUTH SERVICE] Split into ${parts.length} parts using Rails format (--)`);

      if (parts.length !== 3) {
        this.logger.error(`❌ [AUTH SERVICE] Invalid encrypted cookie format. Expected 3 parts, got ${parts.length}`);
        this.logger.error(`🔐 [AUTH SERVICE] First 100 chars of decoded value: ${base64String.substring(0, 100)}`);
        throw new Error('Invalid encrypted cookie format');
      }

      // Decode each part from base64
      const encryptedData = Buffer.from(parts[0], 'base64');
      const iv = Buffer.from(parts[1], 'base64');
      const authTag = Buffer.from(parts[2], 'base64');

      this.logger.log(`🔐 [AUTH SERVICE] Encrypted data length: ${encryptedData.length}, IV length: ${iv.length}, Auth tag length: ${authTag.length}`);

      // Step 4: AES-256-GCM decryption
      this.logger.log(`🔐 [AUTH SERVICE] Step 4: AES-256-GCM decryption...`);

      // Convert hex key to buffer (ensure it's exactly 32 bytes for AES-256)
      const key = Buffer.from(this.config.encryptionKey, 'hex');
      this.logger.log(`🔑 [AUTH SERVICE] Key length: ${key.length} bytes`);

      if (key.length !== 32) {
        throw new Error(`Invalid encryption key length. Expected 32 bytes, got ${key.length}`);
      }

      // Create decipher for GCM mode
      const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv);
      decipher.setAuthTag(authTag);

      // Decrypt
      let decrypted = decipher.update(encryptedData, undefined, 'utf8');
      decrypted += decipher.final('utf8');

      this.logger.log(`✅ [AUTH SERVICE] Decryption successful, result length: ${decrypted.length}`);

      // Remove quotes if present (Rails adds quotes around JSON strings)
      const result = decrypted.replace(/^"(.*)"$/, '$1');
      this.logger.log(`✅ [AUTH SERVICE] Final result length after quote removal: ${result.length}`);
      this.logger.log(`🎯 [AUTH SERVICE] Decrypted token (first 100 chars): ${result.substring(0, 100)}...`);

      return result;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Decryption failed: ${error.message}`);
      this.logger.error(`❌ [AUTH SERVICE] Error stack: ${error.stack}`);
      throw new UnauthorizedException('Failed to decrypt authentication token');
    }
  }

  /**
   * Extract and decrypt tokens from cookies
   */
  async extractTokensFromCookies(cookies: Record<string, string>): Promise<DecryptedTokens> {
    const encryptedAccessToken = cookies[this.config.cookieNames.accessToken];
    const encryptedRefreshToken = cookies[this.config.cookieNames.refreshToken];

    this.logger.log(`🍪 [AUTH SERVICE] Available cookies: ${Object.keys(cookies)}`);
    this.logger.log(`🔍 [AUTH SERVICE] Looking for access token: ${this.config.cookieNames.accessToken}`);
    this.logger.log(`🔍 [AUTH SERVICE] Access token found: ${!!encryptedAccessToken}`);

    if (!encryptedAccessToken) {
      throw new UnauthorizedException('Missing access token');
    }

    try {
      const accessToken = await this.decryptCookie(encryptedAccessToken);
      const refreshToken = encryptedRefreshToken ? await this.decryptCookie(encryptedRefreshToken) : undefined;

      return { accessToken, refreshToken };
    } catch (error) {
      this.logger.error('Failed to decrypt tokens:', error);
      throw new UnauthorizedException('Invalid authentication tokens');
    }
  }

  /**
   * Verify JWT token and extract payload
   * JWKS verification temporarily disabled - extracts payload directly
   */
  async verifyToken(token: string): Promise<JWTPayload> {
    try {
      this.logger.log(`🔍 [AUTH SERVICE] Starting JWT verification...`);
      this.logger.log(`🎫 [AUTH SERVICE] Token length: ${token.length}`);

      // Parse JWT without verification (JWKS client disabled)
      const parts = token.split('.');
      if (parts.length !== 3) {
        this.logger.error(`❌ [AUTH SERVICE] Invalid JWT format. Expected 3 parts, got ${parts.length}`);
        throw new Error('Invalid JWT format');
      }

      // Decode header and payload
      const header = JSON.parse(Buffer.from(parts[0], 'base64').toString());
      const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());

      this.logger.log(`📋 [AUTH SERVICE] JWT Header:`, JSON.stringify(header, null, 2));
      this.logger.log(`📋 [AUTH SERVICE] JWT Payload:`, JSON.stringify(payload, null, 2));

      // Validate required fields based on actual Ruby JWT structure
      if (!payload.sub || !payload.sid || !payload.ent_set || !payload.jti || !payload.azp) {
        this.logger.error(`❌ [AUTH SERVICE] Missing required JWT fields. Payload: ${JSON.stringify(payload)}`);
        throw new UnauthorizedException('Invalid JWT payload - missing required fields');
      }

      this.logger.log(`✅ [AUTH SERVICE] JWT verification successful for user ID: ${payload.sub}`);

      return payload;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] JWT verification failed: ${error.message}`);
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

  /**
   * Get user from JWT payload - returns ONLY actual JWT fields (no computed/hardcoded fields)
   */
  getUserFromPayload(payload: JWTPayload): any {
    // Validate required JWT fields based on actual Ruby payload structure
    if (!payload.sub || !payload.sid || !payload.ent_set || !payload.jti || !payload.azp) {
      this.logger.error(`❌ [AUTH SERVICE] Missing required JWT fields. Payload: ${JSON.stringify(payload)}`);
      throw new UnauthorizedException('Invalid JWT payload - missing required fields');
    }

    this.logger.log(`📋 [AUTH SERVICE] Processing JWT for user: ${payload.sub}`);
    this.logger.log(`📋 [AUTH SERVICE] Entity set: ${payload.ent_set}`);
    this.logger.log(`📋 [AUTH SERVICE] Email present: ${!!payload.email}`);
    this.logger.log(`📋 [AUTH SERVICE] Session ID: ${payload.sid}`);
    this.logger.log(`📋 [AUTH SERVICE] Returning ONLY actual JWT payload fields (no computed fields)`);

    // Return ONLY the actual fields from the JWT payload - no computed, derived, or hardcoded fields
    const actualJwtFields: any = {
      // Standard JWT fields (exactly as they appear in the JWT)
      iss: payload.iss,
      sub: payload.sub,
      aud: payload.aud,
      exp: payload.exp,
      iat: payload.iat,
      jti: payload.jti,

      // NGNair-specific fields (exactly as they appear in the JWT)
      sid: payload.sid,
      azp: payload.azp,
      ent_set: payload.ent_set,
      perm_v: payload.perm_v,
      amr: payload.amr,
      auth_time: payload.auth_time,
    };

    // Only include email if it actually exists in the JWT
    if (payload.email !== undefined) {
      actualJwtFields.email = payload.email;
    }

    this.logger.log(`📋 [AUTH SERVICE] Actual JWT fields being returned: ${JSON.stringify(Object.keys(actualJwtFields))}`);

    return actualJwtFields;
  }

  /**
   * Authenticate user from cookies using proper external auth service integration
   * This is the main method for the /auth/me endpoint
   * Returns the actual JWT payload fields only (no computed/hardcoded fields)
   */
  async authenticateUserFromCookies(cookies: Record<string, string>): Promise<any> {
    this.logger.log('🔐 [AUTH SERVICE] Starting authentication for /auth/me endpoint');
    this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    try {
      // Extract encrypted access token from cookies
      const encryptedAccessToken = cookies[this.config.cookieNames.accessToken];

      if (!encryptedAccessToken) {
        throw new UnauthorizedException('Missing access token');
      }

      this.logger.log(`🔑 [AUTH SERVICE] Found encrypted access token: ${encryptedAccessToken.substring(0, 50)}...`);

      // Step 1: Decrypt the access token using Rails MessageEncryptor format
      this.logger.log('🔓 [AUTH SERVICE] Step 1: Decrypting access token...');
      const decryptedToken = await this.decryptCookie(encryptedAccessToken);
      this.logger.log(`✅ [AUTH SERVICE] Token decrypted successfully, length: ${decryptedToken.length}`);

      // Step 2: Verify and decode JWT payload
      this.logger.log('🔍 [AUTH SERVICE] Step 2: Verifying JWT...');
      const payload = await this.verifyToken(decryptedToken);
      this.logger.log(`✅ [AUTH SERVICE] JWT verified successfully for user: ${payload.sub}`);

      // Step 3: Extract actual JWT fields (no computed fields)
      this.logger.log('👤 [AUTH SERVICE] Step 3: Extracting JWT payload fields...');
      const jwtPayload = this.getUserFromPayload(payload);
      this.logger.log(`✅ [AUTH SERVICE] JWT payload extracted successfully for user: ${jwtPayload.sub}`);

      return jwtPayload;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Authentication failed: ${error.message}`);
      this.logger.error(`❌ [AUTH SERVICE] Error stack: ${error.stack}`);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  /**
   * Get all users from external auth service (EXTERNAL API CALL - for /auth/users endpoint)
   */
  async getAllUsers(cookies: Record<string, string>): Promise<any> {
    try {
      this.logger.log('🔍 [AUTH SERVICE] Getting all users from external service');
      this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

      // Extract and validate access token
      this.logger.log(`🔓 [AUTH SERVICE] Extracting and decrypting tokens...`);
      this.logger.log(`Available cookies: ${Object.keys(cookies)}`);
      this.logger.log(`Looking for access token: ${this.config.cookieNames.accessToken}`);
      this.logger.log(`Looking for refresh token: ${this.config.cookieNames.refreshToken}`);

      const encryptedAccessToken = cookies[this.config.cookieNames.accessToken];
      const encryptedRefreshToken = cookies[this.config.cookieNames.refreshToken];

      this.logger.log(`Access token found: ${!!encryptedAccessToken}`);
      this.logger.log(`Refresh token found: ${!!encryptedRefreshToken}`);

      if (!encryptedAccessToken) {
        throw new UnauthorizedException('Missing access token');
      }

      // Forward the encrypted cookie to external service
      this.logger.log(`🌐 [AUTH SERVICE] Making request to: ${this.config.externalAuthServiceUrl}/users`);
      this.logger.log(`🍪 [AUTH SERVICE] Forwarding cookie: ${this.config.cookieNames.accessToken}=${encryptedAccessToken.substring(0, 50)}...`);

      const response = await firstValueFrom(
        this.httpService.get(`${this.config.externalAuthServiceUrl}/users`, {
          headers: {
            'Cookie': `${this.config.cookieNames.accessToken}=${encryptedAccessToken}`,
          },
          timeout: this.config.requestTimeout || 10000,
        })
      );

      this.logger.log(`✅ [AUTH SERVICE] Successfully retrieved ${Array.isArray(response.data) ? response.data.length : 'unknown'} users`);
      return response.data;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Failed to get all users from external service: ${error.message}`);
      if (error.response) {
        this.logger.error(`❌ [AUTH SERVICE] External service response status: ${error.response.status}`);
        this.logger.error(`❌ [AUTH SERVICE] External service response data: ${JSON.stringify(error.response.data)}`);
      }
      throw new UnauthorizedException('Invalid or expired access token');
    }
  }

  /**
   * Get user by ID from external auth service (EXTERNAL API CALL - for /auth/users/{id} endpoint)
   */
  async getUserById(userId: string, cookies: Record<string, string>): Promise<any> {
    try {
      this.logger.log(`🔍 [AUTH SERVICE] Getting user ${userId} from external service`);
      this.logger.log(`🍪 [AUTH SERVICE] Received cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);

      const encryptedAccessToken = cookies[this.config.cookieNames.accessToken];
      if (!encryptedAccessToken) {
        throw new UnauthorizedException('Missing access token');
      }

      // Forward the encrypted cookie to external service
      this.logger.log(`🌐 [AUTH SERVICE] Making request to: ${this.config.externalAuthServiceUrl}/users/${userId}`);
      this.logger.log(`🍪 [AUTH SERVICE] Forwarding cookie: ${this.config.cookieNames.accessToken}=${encryptedAccessToken.substring(0, 50)}...`);

      const response = await firstValueFrom(
        this.httpService.get(`${this.config.externalAuthServiceUrl}/users/${userId}`, {
          headers: {
            'Cookie': `${this.config.cookieNames.accessToken}=${encryptedAccessToken}`,
          },
          timeout: this.config.requestTimeout || 10000,
        })
      );

      this.logger.log(`✅ [AUTH SERVICE] Successfully retrieved user ${userId}`);
      return response.data;
    } catch (error) {
      this.logger.error(`❌ [AUTH SERVICE] Failed to get user ${userId} from external service: ${error.message}`);
      if (error.response) {
        this.logger.error(`❌ [AUTH SERVICE] External service response status: ${error.response.status}`);
        this.logger.error(`❌ [AUTH SERVICE] External service response data: ${JSON.stringify(error.response.data)}`);
      }
      throw new UnauthorizedException('Invalid or expired access token');
    }
  }
}
