import { Module, DynamicModule, Global } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { PartnerService } from './services/partner.service';
import { PartnerController } from './controllers/partner.controller';
import { PartnerConfig, DEFAULT_PARTNER_CONFIG } from './types/partner.types';

/**
 * Partner integration module for NestJS applications
 * Provides partner API integration, scope resolution, and user management
 */
@Global()
@Module({})
export class PartnerModule {
  /**
   * Register the partner module with configuration
   * @param config Partner configuration
   * @returns Dynamic module
   */
  static register(config: PartnerConfig): DynamicModule {
    // Merge with default configuration
    const mergedConfig: PartnerConfig = {
      ...DEFAULT_PARTNER_CONFIG,
      ...config,
      defaultHeaders: {
        ...DEFAULT_PARTNER_CONFIG.defaultHeaders,
        ...config.defaultHeaders,
      },
    };

    return {
      module: PartnerModule,
      imports: [
        HttpModule.register({
          timeout: mergedConfig.requestTimeout || 10000,
          maxRedirects: 5,
        }),
      ],
      controllers: [PartnerController],
      providers: [
        {
          provide: PartnerConfig,
          useValue: mergedConfig,
        },
        PartnerService,
      ],
      exports: [
        PartnerService,
        PartnerConfig,
      ],
    };
  }

  /**
   * Register the partner module asynchronously with configuration factory
   * @param options Async configuration options
   * @returns Dynamic module
   */
  static registerAsync(options: {
    useFactory: (...args: any[]) => Promise<PartnerConfig> | PartnerConfig;
    inject?: any[];
  }): DynamicModule {
    return {
      module: PartnerModule,
      imports: [
        HttpModule.register({
          timeout: 10000,
          maxRedirects: 5,
        }),
      ],
      controllers: [PartnerController],
      providers: [
        {
          provide: PartnerConfig,
          useFactory: async (...args: any[]) => {
            const config = await options.useFactory(...args);
            return {
              ...DEFAULT_PARTNER_CONFIG,
              ...config,
              defaultHeaders: {
                ...DEFAULT_PARTNER_CONFIG.defaultHeaders,
                ...config.defaultHeaders,
              },
            };
          },
          inject: options.inject || [],
        },
        PartnerService,
      ],
      exports: [
        PartnerService,
        PartnerConfig,
      ],
    };
  }

  /**
   * Register the partner module for services only (no controllers)
   * Useful when you only need the service without exposing REST endpoints
   */
  static forServices(config: PartnerConfig): DynamicModule {
    const mergedConfig: PartnerConfig = {
      ...DEFAULT_PARTNER_CONFIG,
      ...config,
      defaultHeaders: {
        ...DEFAULT_PARTNER_CONFIG.defaultHeaders,
        ...config.defaultHeaders,
      },
    };

    return {
      module: PartnerModule,
      imports: [
        HttpModule.register({
          timeout: mergedConfig.requestTimeout || 10000,
          maxRedirects: 5,
        }),
      ],
      providers: [
        {
          provide: PartnerConfig,
          useValue: mergedConfig,
        },
        PartnerService,
      ],
      exports: [
        PartnerService,
        PartnerConfig,
      ],
    };
  }
}
