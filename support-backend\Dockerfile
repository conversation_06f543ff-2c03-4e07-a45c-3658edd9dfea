# Staging Setup 
FROM node:20-alpine as builder
# Install required dependencies for Prisma
RUN apk add --no-cache openssl postgresql-client
USER node
WORKDIR /home/<USER>
COPY --chown=node:node package*.json ./
RUN npm install --legacy-peer-deps
COPY --chown=node:node . .

RUN npx prisma generate

RUN npm run build \
&& npm i --omit=dev --legacy-peer-deps

# Production Setup---
FROM node:20-alpine
# Install required dependencies for Prisma
RUN apk add --no-cache openssl postgresql-client
USER node
WORKDIR /home/<USER>
COPY --from=builder --chown=node:node /home/<USER>/package*.json ./
COPY --from=builder --chown=node:node /home/<USER>/node_modules/ ./node_modules/
COPY --from=builder --chown=node:node /home/<USER>/dist/ ./dist/
COPY --from=builder --chown=node:node /home/<USER>/prisma ./prisma
COPY --chown=node:node start.sh ./start.sh

EXPOSE ${API_PORT:-3040}

# Add healthcheck
# HEALTHCHECK --interval=30s --timeout=30s --start-period=120s --retries=3 \
#     CMD wget -q --spider http://localhost:${API_PORT:-3000}${API_PREFIX:-/api}/health/ready || exit 1

RUN chmod +x ./start.sh
 
CMD ["./start.sh"]