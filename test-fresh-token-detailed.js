#!/usr/bin/env node

const http = require('http');
const { URL } = require('url');

// Configuration
const BASE_URL = 'http://ng-support-local.dev.dev1.ngnair.com:3040';
const ACCESS_TOKEN = 'ZVZxY3hQRFlueDNZYzhLbU9nZGZnYVlQUllNenBHdGI5R2NLYnd2b2ZLSTNEdWZ0TThFL2M5ODBCNEgrd2daVDNHaU9SVCtnTFZZTUtNZlVETFpvTGU3Z0VDSURkamU4YU5nSXVJQTZhSTYxRU84d1VJZG5zZ0c5a2dWVWY3SVRkUWQySi83WEQzWFhaTkN5UEdDYW5JUFJQb015QWY4dENsaUl0NEtlalBtWG9SeFY0N04xSllJcnMxWnU0MktyZjFjakI5MHBKM3ZkUXNvRXY3d3M5OWUxcWFxdURFdEhhdTlkRjVSWGErWHBNalVUcjJlQ05JWEpPTEZjRG9NVVJ0Z01SUzZuU3k5YUtwKzNIVDBwZEFPdjJRZWVvbHIvdHUzbEZjcWdpTGY3dERqQ1FKWjZYU2tCVXVmNTN0WHF2c0RXdjJoOExQNnB3RTRybnFmT0VpQWhsWHpGRzFLTW10QXRHQTlyUk5yVHJMUENHd3NrOXB6Y04yWVZJbllQVHJZOGE1SEhHSSsrN0ZoK2hlS3pyOHBqRFRIaEZmVTdBMnFmQ2Z5L2lKVFEzdk9hankzTjNJdDdoNjRKaDVlSnRtcFlvdFRWMmIrY1RWZVRpZzV0UzMyRXpsenBUNEhFeER2RTdsV1pDeVJXL1JyNWh1ZkhwczhmbXJHMm9SYW9BUEVLTEtYRW4xV0wvODFtTm14Q0IyY01QUzNDS05PcFNqb1YxQ1ZhRmVUS2dkL0crOUk3c2ZXTWtRZjM4VHdvYjdPQ0cxUy9FV1luL0MreVlGVXFZMUF2dlM0K1JmWnZhenhYRjNpUW92bGpvQzFVMXlpblR5RFMvWHM3LzhTcG8rcWk3Z1R3bTVPM0FzNE1sY1EzeTlGQ1pRM01DRThDTmJVZ3NsanE2b2FnRXZMSkRtUno5c1cvUk9EMzZIcnJqdFo4MVNXSEZ5MVo5dGRTemp6TGlRL2dMQzlWSG1OSUJCZkFBbUtXR2tGNjVieDVXZFhOS2FPb3lZc010SWVOSVlGcmpQUjlnMlgzNnJ2d1FSMC9EbEdhaVM0d0NxbEZZMkd6QnFLbkZMbVhuclRrNFU4ZTdMcDJDbjFQZ3NmdWFNUEM2RDAzUkZOU0dyMzlTSjBQSnlTNUVPU1hjN2t4eFAxTGR6TFRRdnFrOEdxNW1zNi92WnF5ZnFMN05aTEUyWkhXQkRSL2N5UVp2dkVRR1RSZjJjd3NCRnlVVUxaMkc5WVgvcUtrN1dZZmFyWThMYTRhbmdpbG9XUjE4eFFYMENFaVRtV1lOc0taYWI3OEdsQ3BqbEtuSTBOeCtlSVM3UGFjRWtnMjUzdk5EeEcwbC9DTGUwb3UxcnVpZlBiZVU5R010WERORU5LYlJWZEJ5SXY1em84Y29IMDFKalV1OE1BOG1Fc0UwZk9UNDZqamdRbmowNFRzTGVKL0h6VmpBZ3BlSm9zUXk1dHdHVnBFdmQ2UmtRVXpGdkp5aUpSazByTDVaQT09LS1McFU1RTE2RUpKMDhWcDFkLS12Rzc0Tnd5akNndjFSY1FZRmlEMzRRPT0%3D';

console.log('🧪 DETAILED FRESH TOKEN TESTING');
console.log('===============================');
console.log('Testing specific authorization scenarios with fresh token');
console.log('');

// Helper function to make HTTP requests
function makeRequest(endpoint, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const url = new URL(endpoint, BASE_URL);
        const options = {
            hostname: url.hostname,
            port: url.port,
            path: url.pathname + url.search,
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Cookie': `access_token=${ACCESS_TOKEN}`,
                'User-Agent': 'Node.js-Test-Client/1.0'
            }
        };

        if (data) {
            const jsonData = JSON.stringify(data);
            options.headers['Content-Length'] = Buffer.byteLength(jsonData);
        }

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonBody = JSON.parse(body);
                    resolve({
                        success: res.statusCode >= 200 && res.statusCode < 300,
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        data: jsonBody,
                        headers: res.headers
                    });
                } catch (e) {
                    resolve({
                        success: false,
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        data: body,
                        headers: res.headers,
                        parseError: e.message
                    });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

async function runDetailedTests() {
    try {
        console.log('⏳ Waiting 5 seconds for server to be ready...\n');
        
        setTimeout(async () => {
            console.log('🔍 TEST 1: Category Authorization (The Real Test)');
            console.log('═'.repeat(60));
            
            // Test GET categories (should succeed)
            console.log('\n📋 1a. GET /categories (Should succeed for partner:support:user)');
            const getCategoriesResult = await makeRequest('/categories');
            console.log(`Status: ${getCategoriesResult.status}`);
            
            if (getCategoriesResult.success) {
                console.log('✅ CORRECT: Category retrieval allowed');
                console.log(`Found ${getCategoriesResult.data.length} categories`);
            } else {
                console.log('❌ UNEXPECTED: Category retrieval blocked');
                console.log('Response:', JSON.stringify(getCategoriesResult.data, null, 2));
            }
            
            // Test POST categories (should fail)
            console.log('\n📝 1b. POST /categories (Should be blocked for partner:support:user)');
            const categoryData = {
                name: 'Fresh Token Test Category',
                description: 'Testing with fresh token - should be blocked'
            };
            
            const createCategoryResult = await makeRequest('/categories', 'POST', categoryData);
            console.log(`Status: ${createCategoryResult.status}`);
            
            if (!createCategoryResult.success && (createCategoryResult.status === 401 || createCategoryResult.status === 403)) {
                console.log('✅ CORRECT: Category creation properly blocked');
                console.log('Response:', JSON.stringify(createCategoryResult.data, null, 2));
            } else if (createCategoryResult.success) {
                console.log('❌ AUTHORIZATION BYPASS: Category creation succeeded when it should be blocked!');
                console.log('Response:', JSON.stringify(createCategoryResult.data, null, 2));
            } else {
                console.log('❓ UNEXPECTED ERROR');
                console.log('Response:', JSON.stringify(createCategoryResult.data, null, 2));
            }
            
            console.log('\n🎫 TEST 2: Ticket Creation (Data Population Test)');
            console.log('═'.repeat(60));
            
            if (getCategoriesResult.success && getCategoriesResult.data.length > 0) {
                const ticketData = {
                    subject: 'Fresh Token Data Population Test',
                    description: 'Testing data population with fresh token',
                    categoryId: getCategoriesResult.data[0].id,
                    priority: 'MEDIUM'
                };
                
                const createTicketResult = await makeRequest('/tickets', 'POST', ticketData);
                console.log(`Status: ${createTicketResult.status}`);
                
                if (createTicketResult.success) {
                    console.log('✅ Ticket creation successful');
                    
                    const ticket = createTicketResult.data;
                    console.log('\n📊 DATA POPULATION ANALYSIS:');
                    console.log('─'.repeat(40));
                    
                    const fields = {
                        'firstName': ticket.firstName,
                        'lastName': ticket.lastName,
                        'email': ticket.email,
                        'partnerOrgId': ticket.partnerOrgId,
                        'partnerUserId': ticket.partnerUserId,
                        'partnerRole': ticket.partnerRole
                    };
                    
                    let populatedCount = 0;
                    let nullFields = [];
                    
                    for (const [field, value] of Object.entries(fields)) {
                        if (value === null || value === undefined) {
                            nullFields.push(field);
                            console.log(`❌ ${field}: ${value} (NULL)`);
                        } else {
                            populatedCount++;
                            console.log(`✅ ${field}: ${value}`);
                        }
                    }
                    
                    console.log(`\n📈 Summary: ${populatedCount}/${Object.keys(fields).length} fields populated`);
                    if (nullFields.length > 0) {
                        console.log(`🚨 NULL FIELDS: ${nullFields.join(', ')}`);
                    }
                    
                } else {
                    console.log('❌ Ticket creation failed');
                    console.log('Response:', JSON.stringify(createTicketResult.data, null, 2));
                }
            } else {
                console.log('❌ Cannot test ticket creation - no categories available');
            }
            
            console.log('\n🔍 TEST 3: Debug Endpoint (Scope Context Check)');
            console.log('═'.repeat(60));
            
            const debugResult = await makeRequest('/partner-test/debug-auth');
            console.log(`Status: ${debugResult.status}`);
            
            if (debugResult.success) {
                console.log('✅ Debug endpoint accessible');
                const debug = debugResult.data.debug;
                console.log(`Has User: ${debug.hasUser}`);
                console.log(`Has Scope Context: ${debug.hasScopeContext}`);
                
                if (!debug.hasScopeContext) {
                    console.log('\n💡 EXPLANATION: Debug endpoint has no scope decorators');
                    console.log('   The ScopeGuard only runs when endpoints have scope decorators');
                    console.log('   This is why scopeContext is false here, but works on other endpoints');
                }
            }
            
            console.log('\n📊 FINAL ANALYSIS');
            console.log('═'.repeat(50));
            
            console.log('\n🎯 AUTHENTICATION STATUS:');
            console.log('✅ Token decryption: WORKING');
            console.log('✅ JWT verification: WORKING');
            console.log('✅ User authentication: WORKING');
            
            console.log('\n🎯 AUTHORIZATION STATUS:');
            if (getCategoriesResult.success && !createCategoryResult.success) {
                console.log('✅ Scope-based authorization: WORKING CORRECTLY');
                console.log('   • Category retrieval: Allowed (correct)');
                console.log('   • Category creation: Blocked (correct)');
            } else if (createCategoryResult.success) {
                console.log('❌ Scope-based authorization: BYPASS DETECTED');
                console.log('   • Category creation should be blocked but succeeded');
            } else {
                console.log('❓ Scope-based authorization: NEEDS INVESTIGATION');
            }
            
            console.log('\n🎯 DATA POPULATION STATUS:');
            console.log('❌ User data fields still null (firstName, lastName, email)');
            console.log('❌ Partner data fields still null (partnerOrgId)');
            console.log('✅ Scope data fields populated (partnerUserId, partnerRole)');
            
            console.log('\n🔧 REMAINING ISSUES TO INVESTIGATE:');
            console.log('1. Why user data API calls are not populating firstName, lastName, email');
            console.log('2. Why partner data is not being attached to scope context');
            console.log('3. If authorization bypass is occurring, why scope guards are not enforcing');
            
            console.log('\n🏁 Test completed!');
            
        }, 5000);
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run the tests
runDetailedTests().catch(console.error);
