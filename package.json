{"name": "@ngnair/auth-integration-package", "version": "1.0.0", "description": "Complete authentication module package for NestJS applications with Rails-compatible AES-256-GCM cookie decryption and JWT processing", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "prepublishOnly": "npm run build"}, "keywords": ["<PERSON><PERSON><PERSON>", "authentication", "jwt", "rails-compatible", "aes-256-gcm", "cookie-decryption", "auth-module"], "author": "NGNAIR Development Team", "license": "MIT", "peerDependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/axios": "^3.0.0", "@nestjs/platform-fastify": "^10.0.0", "rxjs": "^7.8.0", "reflect-metadata": "^0.1.13"}, "dependencies": {"jsonwebtoken": "^9.0.2", "jwks-client": "^3.0.1", "axios": "^1.5.0"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.3", "@types/node": "^20.0.0", "typescript": "^5.0.0"}, "files": ["dist/**/*", "src/**/*", "config/**/*", "examples/**/*", "docs/**/*", "README.md"], "repository": {"type": "git", "url": "https://github.com/ngnair/auth-integration-package.git"}, "bugs": {"url": "https://github.com/ngnair/auth-integration-package/issues"}, "homepage": "https://github.com/ngnair/auth-integration-package#readme"}