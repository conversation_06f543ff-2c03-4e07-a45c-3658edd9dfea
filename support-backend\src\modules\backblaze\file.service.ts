import { Injectable, Logger, NotFoundException, BadRequestException } from "@nestjs/common";
import { PrismaClient, Prisma, SupportFile } from "@prisma/client";
import { BackblazeService } from "./storage";
import { PrismaService } from "../../prisma/prisma.service";
import { FastifyFileUpload } from "./file.controller";

type SupportFileDelegate = PrismaClient["supportFile"];

@Injectable()
export class FileService {
  private readonly logger = new Logger(FileService.name);
  private readonly db: SupportFileDelegate;

  constructor(
    private readonly prisma: PrismaService,
    private readonly backblazeService: BackblazeService,
  ) {
    this.db = prisma.supportFile;
  }

  async findById(id: string): Promise<SupportFile | null> {
    return this.db.findUnique({
      where: { id }
    });
  }

  async uploadFile(
    file: FastifyFileUpload,
    userId: string,
    ticketId?: string,
    commentId?: string,
  ): Promise<SupportFile> {
    try {
      // Defensive: ensure file is a plain object with only the expected properties
      if (
        !file ||
        typeof file.originalname !== "string" ||
        typeof file.mimetype !== "string" ||
        typeof file.encoding !== "string" ||
        !Buffer.isBuffer(file.buffer) ||
        typeof file.size !== "number" ||
        typeof file.fieldname !== "string"
      ) {
        throw new BadRequestException("Invalid file object passed to service");
      }

      // Validate that at least one of ticketId or commentId is provided
      if (!ticketId && !commentId) {
        throw new BadRequestException("Please provide at least one of ticketId or commentId");
      }

      // Upload file to Backblaze
      const path = ticketId ? `tickets/${ticketId}` : `comments/${commentId}`;
      const filename = `${path}/${Date.now()}-${file.originalname}`;
      // Only pass plain file data to storage
      const { url } = await this.backblazeService.uploadFile(
        {
          originalname: file.originalname,
          mimetype: file.mimetype,
          encoding: file.encoding,
          buffer: file.buffer,
          size: file.size,
          fieldname: file.fieldname,
        },
        filename
      );

      // First validate entities exist if IDs are provided
      if (ticketId) {
        const ticket = await this.prisma.ticket.findUnique({ 
          where: { id: ticketId } 
        });
        if (!ticket) throw new NotFoundException("Ticket not found");
      }

      if (commentId) {
        const comment = await this.prisma.comment.findUnique({
          where: { id: commentId },
          include: { ticket: true }
        });
        if (!comment) throw new NotFoundException("Comment not found");
        if (!comment.ticket) throw new BadRequestException("Comment must belong to a ticket");
      }

      // Prepare plain data for Prisma
      const createData = {
        filename: file.originalname,
        fileUrl: url,
        fileSize: file.size,
        mimeType: file.mimetype,
        uploadedBy: userId,
        ...(ticketId && { ticketId }),
        ...(commentId && { commentId })
      } satisfies Prisma.SupportFileCreateInput;

      // Debug log: show only plain data
      this.logger.log(`Creating file record: ${JSON.stringify({
        filename: createData.filename,
        fileUrl: createData.fileUrl,
        fileSize: createData.fileSize,
        mimeType: createData.mimeType,
        uploadedBy: createData.uploadedBy,
        ticketId: createData.ticketId,
        commentId: createData.commentId
      })}`);
      // Restore the real Prisma create call
      return this.db.create({
        data: createData
      });
    } catch (error) {
      this.logger.error("Failed to upload file:", error instanceof Error ? error.message : "Unknown error");
      throw error;
    }
  }

  async getFiles(ticketId?: string, commentId?: string): Promise<SupportFile[]> {
    const queryInput = {
      where: {
        OR: [
          ...(ticketId ? [{ ticketId }] : []),
          ...(commentId ? [{ commentId }] : [])
        ]
      }
    } satisfies Prisma.SupportFileFindManyArgs;

    return this.db.findMany(queryInput);
  }

  async deleteFile(fileId: string): Promise<SupportFile> {
    // Find the file first
    const whereInput: Prisma.SupportFileWhereUniqueInput = { id: fileId };
    const file = await this.prisma.supportFile.findUnique({ where: whereInput });

    if (!file) {
      throw new NotFoundException("File not found");
    }

    try {
      // Extract key from URL
      const fileUrl = String(file.fileUrl);
      const url = new URL(fileUrl);
      const key = url.pathname.substring(1); // Remove leading slash

      // Delete from Backblaze
      await this.backblazeService.deleteFile(key);

      // Delete from database
      return await this.prisma.supportFile.delete({ where: whereInput });
    } catch (error) {
      this.logger.error("Failed to delete file:", error instanceof Error ? error.message : "Unknown error");
      throw error;
    }
  }
}
