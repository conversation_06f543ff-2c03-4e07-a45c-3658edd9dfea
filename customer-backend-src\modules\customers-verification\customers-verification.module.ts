import { Module } from '@nestjs/common';
import { CustomersVerificationService } from './customers-verification.service';
import { CustomersVerificationController } from './customers-verification.controller';
import { PrismaModule } from '../../prisma/prisma.module';
import { ConfigModule } from '../../config/config.module';
import { SharedGuardsModule } from '../shared/guards.module';

@Module({
  imports: [
    PrismaModule,
    ConfigModule,
    SharedGuardsModule,
  ],
  controllers: [
    CustomersVerificationController,
  ],
  providers: [
    CustomersVerificationService,
  ],
  exports: [
    CustomersVerificationService,
  ],
})
export class CustomersVerificationModule {}
