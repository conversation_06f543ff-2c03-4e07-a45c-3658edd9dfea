import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { User } from '../types/auth.types';

export const getCurrentUser = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): User => {
    const request = ctx.switchToHttp().getRequest();
    return request.user;
  },
);

// Alias for convenience
export const CurrentUser = getCurrentUser;

// GraphQL-compatible user decorator
export const GetCurrentUser = createParamDecorator(
  (data: unknown, context: ExecutionContext): User => {
    // Check if it's a GraphQL context
    const gqlContext = GqlExecutionContext.create(context);
    if (gqlContext.getContext().req) {
      return gqlContext.getContext().req.user;
    }

    // Fallback to HTTP context
    const request = context.switchToHttp().getRequest();
    return request.user;
  },
);
