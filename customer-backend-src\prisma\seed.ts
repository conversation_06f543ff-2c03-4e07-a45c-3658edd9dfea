import { PrismaClient, CustomerStatus, CustomerType, AddressType, PaymentTokenStatus, PaymentTokenType, PaymentProvider } from '@prisma/client';
import { faker } from '@faker-js/faker';
import * as crypto from 'crypto';

const prisma = new PrismaClient();

// Helper function to generate realistic payment token hash
function generateTokenHash(tokenData: string): string {
  return crypto.createHash('sha256').update(tokenData).digest('hex');
}

// Helper function to generate realistic card numbers (fake but formatted correctly)
function generateFakeCardNumber(brand: string): string {
  const prefixes: { [key: string]: string } = {
    'Visa': '4',
    'Mastercard': '5',
    'American Express': '3',
    'Discover': '6'
  };

  const prefix = prefixes[brand] || '4';
  let cardNumber = prefix;

  // Generate remaining digits
  const remainingLength = brand === 'American Express' ? 14 : 15;
  for (let i = 0; i < remainingLength; i++) {
    cardNumber += Math.floor(Math.random() * 10).toString();
  }

  return cardNumber;
}

// Helper function to mask card numbers
function maskCardNumber(cardNumber: string): string {
  if (cardNumber.length === 15) { // Amex
    return `**** ****** *${cardNumber.slice(-4)}`;
  }
  return `**** **** **** ${cardNumber.slice(-4)}`;
}

async function main() {
  console.log('🌱 Starting comprehensive database seeding...');

  // Clear existing data (optional - comment out if you want to keep existing data)
  console.log('🧹 Cleaning existing data...');
  await prisma.paymentToken.deleteMany();
  await prisma.auditLog.deleteMany();
  await prisma.customerPreference.deleteMany();
  await prisma.contact.deleteMany();
  await prisma.address.deleteMany();
  await prisma.customer.deleteMany();

  // Generate realistic test data
  console.log('👥 Creating 100+ realistic customers with payment methods...');

  const addressTypes = [AddressType.HOME, AddressType.WORK, AddressType.BILLING, AddressType.SHIPPING];
  const paymentProviders = [PaymentProvider.ELAVON, PaymentProvider.TSEP, PaymentProvider.NEARPAY, PaymentProvider.STRIPE];
  const paymentTokenTypes = [PaymentTokenType.CARD, PaymentTokenType.BANK_ACCOUNT, PaymentTokenType.DIGITAL_WALLET];
  const cardBrands = ['Visa', 'Mastercard', 'American Express', 'Discover'];

  const countries = ['US', 'CA', 'GB', 'AU', 'DE', 'FR', 'JP', 'BR'];
  const businessTypes = ['Technology', 'Healthcare', 'Finance', 'Retail', 'Manufacturing', 'Consulting', 'Real Estate', 'Education'];

  // Create 120 customers for comprehensive testing
  const customersToCreate = 120;

  console.log(`🚀 Creating ${customersToCreate} customers with comprehensive data...`);

  for (let i = 0; i < customersToCreate; i++) {
    const isBusinessCustomer = faker.datatype.boolean({ probability: 0.3 }); // 30% business customers
    const customerType = isBusinessCustomer ?
      faker.helpers.arrayElement([CustomerType.BUSINESS, CustomerType.ENTERPRISE]) :
      CustomerType.INDIVIDUAL;

    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    const email = faker.internet.email({ firstName, lastName }).toLowerCase();
    const phone = faker.phone.number({ style: 'national' });
    const dateOfBirth = faker.date.birthdate({ min: 18, max: 80, mode: 'age' });

    // Generate realistic status distribution
    const status = faker.helpers.weightedArrayElement([
      { weight: 70, value: CustomerStatus.ACTIVE },
      { weight: 15, value: CustomerStatus.INACTIVE },
      { weight: 10, value: CustomerStatus.PENDING_VERIFICATION },
      { weight: 5, value: CustomerStatus.SUSPENDED }
    ]);

    // Generate verification flags based on status
    const isEmailVerified = status === CustomerStatus.ACTIVE ? faker.datatype.boolean({ probability: 0.9 }) : faker.datatype.boolean({ probability: 0.3 });
    const isPhoneVerified = status === CustomerStatus.ACTIVE ? faker.datatype.boolean({ probability: 0.8 }) : faker.datatype.boolean({ probability: 0.2 });
    const isKycVerified = status === CustomerStatus.ACTIVE ? faker.datatype.boolean({ probability: 0.7 }) : false;
    // Generate business-specific data
    const companyName = isBusinessCustomer ? faker.company.name() : null;
    const taxId = isBusinessCustomer ? faker.string.numeric(9) : null;
    const businessType = isBusinessCustomer ? faker.helpers.arrayElement(businessTypes) : null;

    // Generate tags based on customer profile
    const tags = [];
    if (status === CustomerStatus.ACTIVE) tags.push('active');
    if (isKycVerified) tags.push('verified');
    if (isBusinessCustomer) tags.push('business');
    if (customerType === CustomerType.ENTERPRISE) tags.push('enterprise');
    if (faker.datatype.boolean({ probability: 0.2 })) tags.push('premium');
    if (faker.datatype.boolean({ probability: 0.1 })) tags.push('high-value');

    const notes = faker.datatype.boolean({ probability: 0.6 }) ?
      faker.lorem.sentence() : null;

    // Generate 1-3 addresses per customer
    const numAddresses = faker.number.int({ min: 1, max: 3 });
    const addresses = [];

    for (let j = 0; j < numAddresses; j++) {
      const country = faker.helpers.arrayElement(countries);
      const addressType = j === 0 ? AddressType.HOME : faker.helpers.arrayElement(addressTypes);

      addresses.push({
        type: addressType,
        label: addressType === AddressType.HOME ? 'Home' :
               addressType === AddressType.WORK ? 'Office' :
               addressType === AddressType.BILLING ? 'Billing' : 'Shipping',
        street1: faker.location.streetAddress(),
        street2: faker.datatype.boolean({ probability: 0.3 }) ? faker.location.secondaryAddress() : null,
        city: faker.location.city(),
        state: country === 'US' ? faker.location.state({ abbreviated: true }) : faker.location.state(),
        postalCode: country === 'US' ? faker.location.zipCode() : faker.location.zipCode(),
        country: country,
        isDefault: j === 0,
        isVerified: faker.datatype.boolean({ probability: 0.7 }),
        latitude: faker.datatype.boolean({ probability: 0.4 }) ? faker.location.latitude() : null,
        longitude: faker.datatype.boolean({ probability: 0.4 }) ? faker.location.longitude() : null,
      });
    }
    // Create customer with addresses
    const customer = await prisma.customer.create({
      data: {
        firstName,
        lastName,
        email,
        phone,
        dateOfBirth,
        companyName,
        taxId,
        businessType,
        status,
        type: customerType,
        isEmailVerified,
        isPhoneVerified,
        isKycVerified,
        tags,
        notes,
        addresses: {
          create: addresses
        }
      },
      include: {
        addresses: true,
      },
    });

    // Generate 1-4 payment methods per customer (only for active customers mostly)
    if (status === CustomerStatus.ACTIVE || faker.datatype.boolean({ probability: 0.3 })) {
      const numPaymentMethods = faker.number.int({ min: 1, max: 4 });

      for (let k = 0; k < numPaymentMethods; k++) {
        const provider = faker.helpers.arrayElement(paymentProviders);
        const tokenType = faker.helpers.arrayElement(paymentTokenTypes);
        const cardBrand = tokenType === PaymentTokenType.CARD ? faker.helpers.arrayElement(cardBrands) : null;

        // Generate realistic token data
        const rawToken = faker.string.alphanumeric(32);
        const tokenHash = generateTokenHash(rawToken);

        let maskedInfo = '';
        let expiresAt = null;

        if (tokenType === PaymentTokenType.CARD && cardBrand) {
          const cardNumber = generateFakeCardNumber(cardBrand);
          maskedInfo = maskCardNumber(cardNumber);
          expiresAt = faker.date.future({ years: 4 });
        } else if (tokenType === PaymentTokenType.BANK_ACCOUNT) {
          maskedInfo = `****${faker.string.numeric(4)}`;
        } else {
          maskedInfo = `${cardBrand || 'Digital'} Wallet`;
        }
        // Generate provider-specific data
        let providerData = {};

        if (provider === PaymentProvider.ELAVON) {
          providerData = {
            elavonToken: `ssl_${faker.string.alphanumeric(16)}`,
            elavonTransactionId: faker.string.numeric(10),
            elavonReferenceId: faker.string.alphanumeric(12),
            elavonCardType: cardBrand === 'Visa' ? 'VISA' :
                           cardBrand === 'Mastercard' ? 'MAST' :
                           cardBrand === 'American Express' ? 'AMEX' : 'DISC',
            elavonApprovalCode: faker.string.alphanumeric(6),
          };
        } else if (provider === PaymentProvider.TSEP) {
          providerData = {
            tsepToken: `tsep_${faker.string.alphanumeric(20)}`,
            tsepTransactionId: faker.string.numeric(12),
            tsepTransactionKey: faker.string.alphanumeric(16),
            tsepDeviceId: faker.string.alphanumeric(8),
            tsepCardType: cardBrand === 'Visa' ? 'V' :
                         cardBrand === 'Mastercard' ? 'M' :
                         cardBrand === 'American Express' ? 'A' : 'D',
            tsepResponseCode: '00', // Success code
          };
        }

        // Generate transaction data
        const transactionAmount = faker.number.float({ min: 10, max: 5000, fractionDigits: 2 });
        const currency = 'USD';

        // Generate finance integration IDs (some customers have them, some don't)
        const financeId = faker.datatype.boolean({ probability: 0.6 }) ? faker.string.uuid() : null;
        const accountId = faker.datatype.boolean({ probability: 0.6 }) ? faker.string.uuid() : null;
        // Create payment token
        await prisma.paymentToken.create({
          data: {
            customerId: customer.id,
            tokenHash,
            externalTokenId: `ext_${faker.string.alphanumeric(16)}`,
            paymentProvider: provider,
            tokenType,
            status: faker.helpers.weightedArrayElement([
              { weight: 85, value: PaymentTokenStatus.ACTIVE },
              { weight: 10, value: PaymentTokenStatus.EXPIRED },
              { weight: 3, value: PaymentTokenStatus.SUSPENDED },
              { weight: 2, value: PaymentTokenStatus.REVOKED }
            ]),
            maskedInfo,
            paymentBrand: cardBrand,
            expiresAt,
            transactionAmount,
            currency,
            customerName: `${firstName} ${lastName}`,
            customerEmail: email,
            financeId,
            accountId,
            createdByIp: faker.internet.ip(),
            lastUsedAt: faker.datatype.boolean({ probability: 0.7 }) ? faker.date.recent({ days: 30 }) : null,
            usageCount: faker.number.int({ min: 0, max: 50 }),
            rawWebhookData: {
              provider: provider,
              timestamp: new Date().toISOString(),
              amount: transactionAmount,
              currency: currency,
              status: 'success'
            },
            providerMetadata: {
              provider: provider,
              ...providerData
            },
            ...providerData
          }
        });
      }
    }

    if ((i + 1) % 10 === 0) {
      console.log(`✅ Created ${i + 1}/${customersToCreate} customers...`);
    }
  }

  console.log('🎉 Database seeding completed successfully!');

  // Generate summary statistics
  const totalCustomers = await prisma.customer.count();
  const totalAddresses = await prisma.address.count();
  const totalPaymentTokens = await prisma.paymentToken.count();

  const customersByStatus = await prisma.customer.groupBy({
    by: ['status'],
    _count: {
      status: true,
    },
  });

  const customersByType = await prisma.customer.groupBy({
    by: ['type'],
    _count: {
      type: true,
    },
  });

  const paymentTokensByProvider = await prisma.paymentToken.groupBy({
    by: ['paymentProvider'],
    _count: {
      paymentProvider: true,
    },
  });

  console.log('\n📊 SEEDING SUMMARY:');
  console.log(`👥 Total Customers: ${totalCustomers}`);
  console.log(`📍 Total Addresses: ${totalAddresses}`);
  console.log(`💳 Total Payment Tokens: ${totalPaymentTokens}`);

  console.log('\n📈 Customer Status Distribution:');
  customersByStatus.forEach(item => {
    console.log(`   ${item.status}: ${item._count.status}`);
  });

  console.log('\n🏢 Customer Type Distribution:');
  customersByType.forEach(item => {
    console.log(`   ${item.type}: ${item._count.type}`);
  });

  console.log('\n💰 Payment Provider Distribution:');
  paymentTokensByProvider.forEach(item => {
    console.log(`   ${item.paymentProvider}: ${item._count.paymentProvider}`);
  });

  console.log('\n✨ Realistic test data generated successfully!');
  console.log('🚀 Ready for development and testing!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
