import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  Logger,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { Prisma } from '@prisma/client';
import { CustomersQueryService, CustomerWithRelations } from './customers-query.service';
import {
  CustomerResponseDto,
  CustomerFilterDto,
  PaginationDto,
  CustomerListResponseDto,
} from '../customers-shared/dto/customer-shared.dto';

@ApiTags('customers-query')
@Controller('customers')
export class CustomersQueryController {
  private readonly logger = new Logger(CustomersQueryController.name);

  constructor(private readonly customersQueryService: CustomersQueryService) {}

  /**
   * Convert CustomerWithRelations to CustomerResponseDto
   */
  private mapToResponseDto(customer: CustomerWithRelations): CustomerResponseDto {
    return {
      ...customer,
      externalId: customer.externalId || undefined,
    } as CustomerResponseDto;
  }

  /**
   * Convert array of CustomerWithRelations to CustomerResponseDto array
   */
  private mapToResponseDtoArray(customers: CustomerWithRelations[]): CustomerResponseDto[] {
    return customers.map(customer => this.mapToResponseDto(customer));
  }

  @Get('statistics')
  @ApiOperation({ 
    summary: 'Get customer statistics and analytics',
    description: 'Retrieve comprehensive customer statistics including counts by status, type, verification status, and recent activity metrics'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number', description: 'Total number of customers' },
        active: { type: 'number', description: 'Number of active customers' },
        inactive: { type: 'number', description: 'Number of inactive customers' },
        suspended: { type: 'number', description: 'Number of suspended customers' },
        pendingVerification: { type: 'number', description: 'Number of customers pending verification' },
        blocked: { type: 'number', description: 'Number of blocked customers' },
        emailVerified: { type: 'number', description: 'Number of customers with verified email' },
        phoneVerified: { type: 'number', description: 'Number of customers with verified phone' },
        kycVerified: { type: 'number', description: 'Number of KYC verified customers' },
        individual: { type: 'number', description: 'Number of individual customers' },
        business: { type: 'number', description: 'Number of business customers' },
        enterprise: { type: 'number', description: 'Number of enterprise customers' },
        recentlyCreated: { type: 'number', description: 'Number of recently created customers' },
        recentlyActive: { type: 'number', description: 'Number of recently active customers' },
      },
    },
  })
  async getCustomerStatistics() {
    this.logger.log(`Getting customer statistics`);
    return this.customersQueryService.getStatistics();
  }

  @Get()
  @ApiOperation({ 
    summary: 'Get all customers with filtering and pagination',
    description: 'Retrieve a paginated list of customers with optional filtering by status, type, verification status, and search terms'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of customers retrieved successfully',
    type: CustomerListResponseDto,
  })
  @ApiQuery({ name: 'search', required: false, description: 'Search term for customer name, email, or phone' })
  @ApiQuery({ name: 'status', required: false, enum: ['ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING_VERIFICATION', 'BLOCKED'], description: 'Filter by customer status' })
  @ApiQuery({ name: 'type', required: false, enum: ['INDIVIDUAL', 'BUSINESS', 'ENTERPRISE'], description: 'Filter by customer type' })
  @ApiQuery({ name: 'isEmailVerified', required: false, type: Boolean, description: 'Filter by email verification status' })
  @ApiQuery({ name: 'isKycVerified', required: false, type: Boolean, description: 'Filter by KYC verification status' })
  @ApiQuery({ name: 'tags', required: false, type: [String], description: 'Filter by customer tags' })
  @ApiQuery({ name: 'skip', required: false, type: Number, description: 'Number of records to skip for pagination' })
  @ApiQuery({ name: 'take', required: false, type: Number, description: 'Number of records to take (max 100)' })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Field to sort by (e.g., createdAt, email, firstName)' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Sort order' })
  async getCustomers(
    @Query() filter: CustomerFilterDto,
    @Query() pagination: PaginationDto,
  ): Promise<CustomerListResponseDto> {
    this.logger.log(`Getting customers`);

    const orderBy: Prisma.CustomerOrderByWithRelationInput = pagination.sortBy ? {
      [pagination.sortBy]: (pagination.sortOrder || 'desc') as Prisma.SortOrder
    } : { createdAt: Prisma.SortOrder.desc };

    const [customers, total] = await Promise.all([
      this.customersQueryService.findMany(
        filter,
        {
          skip: pagination.skip,
          take: pagination.take,
          orderBy,
        },
      ),
      this.customersQueryService.count(filter),
    ]);

    return {
      data: this.mapToResponseDtoArray(customers),
      total,
      skip: pagination.skip || 0,
      take: pagination.take || 20,
    };
  }

  @Get(':id')
  @ApiOperation({ 
    summary: 'Get customer by ID',
    description: 'Retrieve detailed information for a specific customer by their unique identifier'
  })
  @ApiParam({ name: 'id', description: 'Customer unique identifier (UUID)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer retrieved successfully',
    type: CustomerResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Customer not found',
  })
  async getCustomer(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<CustomerResponseDto> {
    this.logger.log(`Getting customer ${id}`);
    const customer = await this.customersQueryService.findById(id);
    if (!customer) {
      throw new Error('Customer not found');
    }
    return this.mapToResponseDto(customer);
  }

  @Get('email/:email')
  @ApiOperation({ 
    summary: 'Get customer by email address',
    description: 'Retrieve customer information using their email address as the lookup key'
  })
  @ApiParam({ name: 'email', description: 'Customer email address' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Customer retrieved successfully',
    type: CustomerResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Customer not found',
  })
  async getCustomerByEmail(
    @Param('email') email: string,
  ): Promise<CustomerResponseDto | null> {
    this.logger.log(`Getting customer by email ${email}`);
    const customer = await this.customersQueryService.findByEmail(email);
    return customer ? this.mapToResponseDto(customer) : null;
  }

  @Post('search')
  @ApiOperation({ 
    summary: 'Advanced customer search with complex filters',
    description: 'Perform advanced search operations with complex filtering criteria and pagination options'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Search results retrieved successfully',
    type: CustomerListResponseDto,
  })
  async searchCustomers(
    @Body() searchRequest: {
      filter?: CustomerFilterDto;
      pagination?: PaginationDto;
    },
  ): Promise<CustomerListResponseDto> {
    this.logger.log(`Advanced search`);

    const { filter, pagination } = searchRequest;

    const orderBy: Prisma.CustomerOrderByWithRelationInput = pagination?.sortBy ? {
      [pagination.sortBy]: (pagination.sortOrder || 'desc') as Prisma.SortOrder
    } : { createdAt: Prisma.SortOrder.desc };

    const [customers, total] = await Promise.all([
      this.customersQueryService.findMany(
        filter,
        {
          skip: pagination?.skip,
          take: pagination?.take,
          orderBy,
        },
      ),
      this.customersQueryService.count(filter),
    ]);

    return {
      data: this.mapToResponseDtoArray(customers),
      total,
      skip: pagination?.skip || 0,
      take: pagination?.take || 20,
    };
  }
}
