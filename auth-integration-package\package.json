{"name": "@ngnair/auth-integration-package", "version": "2.0.0", "description": "Complete authentication module package with Rails-compatible AES-256-GCM cookie decryption and JWT processing for NestJS applications", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "keywords": ["<PERSON><PERSON><PERSON>", "authentication", "jwt", "rails-compatible", "aes-256-gcm", "cookie-decryption", "auth-module"], "author": "NGNair Development Team", "license": "MIT", "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/axios": "^3.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/graphql": "^12.0.0", "@nestjs/apollo": "^12.0.0", "apollo-server-express": "^3.12.0", "graphql": "^16.8.0", "axios": "^1.6.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "jwks-client": "^3.0.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1"}, "devDependencies": {"@types/node": "^20.0.0", "@types/passport-jwt": "^4.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "jest": "^29.5.0", "prettier": "^3.0.0", "typescript": "^5.1.3"}, "peerDependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "reflect-metadata": "^0.1.13"}, "files": ["dist/**/*", "README.md", "config/**/*", "docs/**/*", "examples/**/*"], "repository": {"type": "git", "url": "https://github.com/ngnair/auth-integration-package.git"}, "bugs": {"url": "https://github.com/ngnair/auth-integration-package/issues"}, "homepage": "https://github.com/ngnair/auth-integration-package#readme"}