/**
 * Authentication configuration interface
 * This interface defines all the configuration options needed for the auth module
 */
export interface AuthConfig {
  /**
   * AES-256 encryption key for decrypting Rails-compatible cookies
   * Must be a 64-character hexadecimal string (32 bytes)
   * Example: "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456"
   */
  encryptionKey: string;

  /**
   * JWKS URI for JWT verification (optional)
   * If not provided, JWT verification will be skipped (development mode)
   * Example: "https://auth.example.com/.well-known/jwks.json"
   */
  jwksUri?: string;

  /**
   * External authentication service base URL
   * Used for /auth/users and /auth/users/{id} endpoints
   * Example: "https://auth.example.com/api"
   */
  externalAuthServiceUrl: string;

  /**
   * Cookie names configuration
   */
  cookieNames: {
    /**
     * Name of the access token cookie
     * Default: "access_token"
     */
    accessToken: string;
    
    /**
     * Name of the refresh token cookie
     * Default: "refresh_token"
     */
    refreshToken: string;
  };

  /**
   * Request timeout for external API calls (in milliseconds)
   * Default: 10000 (10 seconds)
   */
  requestTimeout?: number;

  /**
   * Enable debug logging
   * Default: false
   */
  enableDebugLogging?: boolean;

  /**
   * JWT verification options
   */
  jwt?: {
    /**
     * JWT issuer to verify
     */
    issuer?: string;
    
    /**
     * JWT audience to verify
     */
    audience?: string;
    
    /**
     * Skip JWT verification (development only)
     * Default: false
     */
    skipVerification?: boolean;
  };
}

/**
 * Default authentication configuration
 */
export const DEFAULT_AUTH_CONFIG: Partial<AuthConfig> = {
  cookieNames: {
    accessToken: 'access_token',
    refreshToken: 'refresh_token',
  },
  requestTimeout: 10000,
  enableDebugLogging: false,
  jwt: {
    skipVerification: false,
  },
};

/**
 * Environment variable mapping for configuration
 */
export const AUTH_CONFIG_ENV_MAPPING = {
  encryptionKey: 'ACCESS_TOKEN_ENCRYPTION_KEY',
  jwksUri: 'JWKS_URI',
  externalAuthServiceUrl: 'EXTERNAL_AUTH_SERVICE_URL',
  'cookieNames.accessToken': 'ACCESS_TOKEN_COOKIE_NAME',
  'cookieNames.refreshToken': 'REFRESH_TOKEN_COOKIE_NAME',
  requestTimeout: 'AUTH_REQUEST_TIMEOUT',
  enableDebugLogging: 'AUTH_DEBUG_LOGGING',
  'jwt.issuer': 'JWT_ISSUER',
  'jwt.audience': 'JWT_AUDIENCE',
  'jwt.skipVerification': 'JWT_SKIP_VERIFICATION',
} as const;
