# @ngnair/partner-integration-package

Complete partner integration module package for NestJS applications with partner API calls, scope resolution, and user management.

## Features

- 🏢 **Partner API Integration** - Complete partner API client with all endpoints
- 🔐 **Scope Resolution** - User scope resolution and management
- 👥 **User Management** - Partner user scope and permission handling
- 🛡️ **Type Safety** - Full TypeScript support with comprehensive types
- 🔧 **Configurable** - Flexible configuration options
- 📦 **Modular** - Use only what you need (services only or full module)
- 🚀 **Production Ready** - Built for enterprise applications

## Installation

```bash
npm install @ngnair/partner-integration-package
```

## Quick Start

### Basic Usage

```typescript
import { Module } from '@nestjs/common';
import { PartnerModule } from '@ngnair/partner-integration-package';

@Module({
  imports: [
    PartnerModule.register({
      partnerApiBaseUrl: 'https://ng-partner-dev.dev1.ngnair.com/api/v1',
      enableDebugLogging: true,
    }),
  ],
})
export class AppModule {}
```

### Service Only (No Controllers)

```typescript
import { Module } from '@nestjs/common';
import { PartnerModule } from '@ngnair/partner-integration-package';

@Module({
  imports: [
    PartnerModule.forServices({
      partnerApiBaseUrl: 'https://ng-partner-dev.dev1.ngnair.com/api/v1',
    }),
  ],
})
export class AppModule {}
```

### Async Configuration

```typescript
import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PartnerModule } from '@ngnair/partner-integration-package';

@Module({
  imports: [
    PartnerModule.registerAsync({
      useFactory: (configService: ConfigService) => ({
        partnerApiBaseUrl: configService.get('PARTNER_API_BASE_URL'),
        enableDebugLogging: configService.get('NODE_ENV') === 'development',
      }),
      inject: [ConfigService],
    }),
  ],
})
export class AppModule {}
```

## Usage Examples

### Using the Partner Service

```typescript
import { Injectable } from '@nestjs/common';
import { PartnerService } from '@ngnair/partner-integration-package';

@Injectable()
export class MyService {
  constructor(private readonly partnerService: PartnerService) {}

  async getPartnerInfo(partnerId: string, accessToken: string) {
    const result = await this.partnerService.getPartnerInfo(
      { partnerId },
      accessToken
    );
    
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error);
    }
  }

  async getUserScopes(userId: string, partnerId: string, accessToken: string) {
    const result = await this.partnerService.getUserScopes(
      { userId, partnerId, tenantType: 'partner' },
      accessToken
    );
    
    return result;
  }

  async resolveScopeContext(userId: string, partnerId: string, accessToken: string) {
    const result = await this.partnerService.resolveScopeContext(
      { userId, partnerId, tenantType: 'partner' },
      accessToken
    );
    
    return result;
  }
}
```

## API Endpoints

When using the full module (with controllers), the following endpoints are available:

- `GET /partner-integration/partners` - Get all partners
- `GET /partner-integration/partner-info?partner_id=xxx` - Get partner information
- `GET /partner-integration/user-scopes?user_id=xxx&partner_id=xxx` - Get user scopes
- `GET /partner-integration/scope-context?user_id=xxx&partner_id=xxx` - Resolve complete scope context

## Configuration

```typescript
interface PartnerConfig {
  partnerApiBaseUrl: string;
  requestTimeout?: number; // Default: 10000ms
  defaultHeaders?: Record<string, string>;
  enableDebugLogging?: boolean; // Default: false
}
```

## Types

The package exports comprehensive TypeScript types:

```typescript
import {
  PartnerInfo,
  UserScopes,
  ScopeContext,
  PartnerApiResponse,
  TenantType,
} from '@ngnair/partner-integration-package';
```

## License

MIT
