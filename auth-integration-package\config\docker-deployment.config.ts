import { AuthConfig } from '../src/types/auth-config.interface';

/**
 * Docker deployment configuration for NGNair Support Backend
 * This configuration is optimized for the Docker-deployed authentication system
 * using the domain: ng-support-local.dev.dev1.ngnair.com:3040
 */
export const DOCKER_AUTH_CONFIG: AuthConfig = {
  /**
   * AES-256 encryption key for decrypting Rails-compatible cookies
   * This matches the key configured in the Docker deployment
   */
  encryptionKey: 'b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8',

  /**
   * JWKS URI for JWT verification
   * Points to the NGNair authentication service
   */
  jwksUri: 'https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks',

  /**
   * External authentication service base URL
   * Points to the NGNair authentication service API
   */
  externalAuthServiceUrl: 'https://ng-auth-dev.dev1.ngnair.com/api',

  /**
   * <PERSON>ie names configuration
   * Matches the Rails application cookie naming convention
   */
  cookieNames: {
    accessToken: 'access_token',
    refreshToken: 'refresh_token',
  },

  /**
   * Request timeout for external API calls (15 seconds)
   * Increased for Docker network latency
   */
  requestTimeout: 15000,

  /**
   * Enable debug logging for Docker deployment troubleshooting
   */
  enableDebugLogging: true,

  /**
   * JWT verification options
   * Configured for NGNair JWT structure
   */
  jwt: {
    issuer: 'https://ngnair.com',
    audience: 'partner',
    skipVerification: false,
  },
};

/**
 * Environment-based configuration factory
 * Allows overriding Docker config with environment variables
 */
export function createDockerAuthConfig(overrides: Partial<AuthConfig> = {}): AuthConfig {
  return {
    ...DOCKER_AUTH_CONFIG,
    ...overrides,
    cookieNames: {
      ...DOCKER_AUTH_CONFIG.cookieNames,
      ...overrides.cookieNames,
    },
    jwt: {
      ...DOCKER_AUTH_CONFIG.jwt,
      ...overrides.jwt,
    },
  };
}

/**
 * Docker deployment endpoints
 * These are the endpoints available in the Docker-deployed backend
 */
export const DOCKER_ENDPOINTS = {
  /**
   * Support Backend API Base URL
   */
  SUPPORT_API_BASE: 'http://ng-support-local.dev.dev1.ngnair.com:3040',
  
  /**
   * Authentication endpoints
   */
  AUTH: {
    ME: '/auth/me',
    USERS: '/auth/users',
    USER_BY_ID: '/auth/users/:id',
  },
  
  /**
   * GraphQL endpoint
   */
  GRAPHQL: '/graphql',
  
  /**
   * Health check endpoint
   */
  HEALTH: '/health',
} as const;

/**
 * Docker network configuration
 * For internal container-to-container communication
 */
export const DOCKER_NETWORK_CONFIG = {
  /**
   * Internal container network address
   */
  INTERNAL_API_BASE: 'http://support-backend-api:3040',
  
  /**
   * Database connection (for direct access if needed)
   */
  DATABASE_URL: '******************************************************/support_backend',
} as const;
