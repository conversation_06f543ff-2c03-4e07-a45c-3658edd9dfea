import { Injectable, Logger, BadRequestException, UnauthorizedException } from '@nestjs/common';

import { AuthUser } from '../auth-shared';
import { TransactionRequest, OtpVerificationRequest, TransactionResponse, PaymentDetails } from './types';

@Injectable()
export class TransactionService {
  private readonly logger = new Logger(TransactionService.name);

  constructor() {}

  /**
   * Initiate a transaction and send OTP
   */
  async initiateTransaction(user: AuthUser, transactionData: TransactionRequest): Promise<TransactionResponse> {
    try {
      this.logger.log(`Initiating transaction ${transactionData.transactionId} for user: ${user.email}`);

      // Validate user has phone number
      if (!user.phone) {
        throw new BadRequestException('Phone number is required for transaction processing');
      }

      // Validate transaction amount
      if (transactionData.amount <= 0) {
        throw new BadRequestException('Transaction amount must be greater than 0');
      }

      // Simulate sending OTP to external service
      await this.sendOtp(user.phone, transactionData.transactionId);

      // Return transaction response with user details including nested phone
      return {
        transactionId: transactionData.transactionId,
        status: 'otp_sent',
        message: `OTP sent to ${this.maskPhoneNumber(user.phone)}`,
        userDetails: {
          id: user.id,
          email: user.email,
          password: user.password,
          firstName: user.firstName,
          lastName: user.lastName,
          phone: {
            number: user.phone,
            masked: this.maskPhoneNumber(user.phone)
          },
          country: user.country,
          verifiedEmail: user.verifiedEmail,
          verifiedPhone: user.verifiedPhone,
          totpSecret: user.totpSecret,
          role: user.role,
          createdAt: user.createdAt,
          partnerId: user.partnerId,
          mfaEnabled: user.mfaEnabled,
          active: user.active,
          accountId: user.accountId,
          isAdmin: user.isAdmin || false,
          permissions: user.permissions || [],
        },
      };
    } catch (error) {
      this.logger.error(`Failed to initiate transaction: ${error.message}`);
      throw new BadRequestException(`Transaction initiation failed: ${error.message}`);
    }
  }

  /**
   * Verify OTP and complete transaction
   */
  async verifyOtpAndCompleteTransaction(user: AuthUser, otpData: OtpVerificationRequest): Promise<TransactionResponse> {
    try {
      this.logger.log(`Verifying OTP for transaction: ${otpData.transactionId}`);

      // Validate phone number matches user's phone
      if (user.phone !== otpData.phoneNumber) {
        throw new UnauthorizedException('Phone number does not match user account');
      }

      // Verify OTP with external service (demo implementation)
      const isOtpValid = await this.verifyOtp(otpData.phoneNumber, otpData.otp, otpData.transactionId);

      if (!isOtpValid) {
        return {
          transactionId: otpData.transactionId,
          status: 'failed',
          message: 'Invalid OTP. Please try again.',
        };
      }

      // Generate payment details after successful OTP verification
      const paymentDetails = await this.generatePaymentDetails(user, otpData.transactionId);

      return {
        transactionId: otpData.transactionId,
        status: 'completed',
        message: 'Transaction completed successfully',
        userDetails: {
          id: user.id,
          email: user.email,
          password: user.password,
          firstName: user.firstName,
          lastName: user.lastName,
          phone: {
            number: user.phone,
            masked: this.maskPhoneNumber(user.phone)
          },
          country: user.country,
          verifiedEmail: user.verifiedEmail,
          verifiedPhone: user.verifiedPhone,
          totpSecret: user.totpSecret,
          role: user.role,
          createdAt: user.createdAt,
          partnerId: user.partnerId,
          mfaEnabled: user.mfaEnabled,
          active: user.active,
          accountId: user.accountId,
          isAdmin: user.isAdmin || false,
          permissions: user.permissions || [],
        },
        paymentDetails,
      };
    } catch (error) {
      this.logger.error(`Failed to verify OTP: ${error.message}`);
      throw new BadRequestException(`OTP verification failed: ${error.message}`);
    }
  }

  /**
   * Send OTP to external service (simulation)
   */
  private async sendOtp(phoneNumber: string, transactionId: string): Promise<boolean> {
    try {
      this.logger.log(`Sending OTP to ${this.maskPhoneNumber(phoneNumber)} for transaction: ${transactionId}`);
      
      // Simulate external OTP service call
      // In real implementation, this would call an actual OTP service
      await new Promise(resolve => setTimeout(resolve, 100));
      
      this.logger.log(`OTP sent successfully to ${this.maskPhoneNumber(phoneNumber)}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to send OTP: ${error.message}`);
      throw error;
    }
  }

  /**
   * Verify OTP with external service (simulation)
   */
  private async verifyOtp(phoneNumber: string, otp: string, transactionId: string): Promise<boolean> {
    try {
      this.logger.log(`Verifying OTP for ${this.maskPhoneNumber(phoneNumber)}, transaction: ${transactionId}`);
      
      // Demo implementation: accept "123456" as valid OTP
      const isValid = otp === '123456';
      
      this.logger.log(`OTP verification result: ${isValid ? 'SUCCESS' : 'FAILED'}`);
      return isValid;
    } catch (error) {
      this.logger.error(`Failed to verify OTP: ${error.message}`);
      return false;
    }
  }

  /**
   * Generate payment details after successful verification
   */
  private async generatePaymentDetails(user: AuthUser, transactionId: string): Promise<PaymentDetails> {
    try {
      this.logger.log(`Generating payment details for user: ${user.email}, transaction: ${transactionId}`);
      
      // Simulate payment details generation
      const paymentDetails: PaymentDetails = {
        routingNumber: '*********',
        cvv: '123',
        accountNumber: '****1234',
        expiryDate: '12/25',
      };
      
      this.logger.log(`Payment details generated successfully for transaction: ${transactionId}`);
      return paymentDetails;
    } catch (error) {
      this.logger.error(`Failed to generate payment details: ${error.message}`);
      throw error;
    }
  }

  /**
   * Mask phone number for security
   */
  private maskPhoneNumber(phoneNumber: string): string {
    if (!phoneNumber || phoneNumber.length < 4) {
      return '***';
    }
    
    const lastFour = phoneNumber.slice(-4);
    const masked = phoneNumber.slice(0, -4).replace(/\d/g, '*');
    return `${masked}${lastFour}`;
  }
}
