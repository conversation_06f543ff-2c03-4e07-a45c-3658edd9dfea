export interface Config {
    readonly API_PORT: number;
    readonly API_PREFIX: string;
    readonly SWAGGER_ENABLE: number;
    readonly JWT_SECRET: string;
    readonly JWT_ISSUER: string;
    readonly JWT_EXPIRATION?: string;
    readonly B2_KEY_ID: string;
    readonly B2_APP_KEY: string;
    readonly B2_BUCKET_ID: string;
    readonly B2_ENDPOINT: string;
    readonly PARTNER_WEBHOOK_URL?: string;
    readonly PARTNER_EMAIL_CONFIG?: {
        apiUrl: string;
        recipients?: string[];
        teamDistributionList?: string;
        escalationDistributionList?: string;
        defaultFrom?: string;
        replyTo?: string;
    };
    readonly SOFTWARE_PARTNER_WEBHOOK_URL?: string;
    readonly ALLOWED_ORIGINS?: string;

    // Authentication configuration
    readonly AUTH_JWKS_URL?: string;
    readonly ACCESS_TOKEN_ENCRYPTION_KEY?: string;
    readonly AUTH_SERVICE_URL?: string;
}
