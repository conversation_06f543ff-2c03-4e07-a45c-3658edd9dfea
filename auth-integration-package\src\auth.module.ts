import { Module, DynamicModule, Global } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { AuthResolver } from './resolvers/auth.resolver';
import { AuthGuard, OptionalAuthGuard } from './guards/auth.guard';
import { AuthConfig, DEFAULT_AUTH_CONFIG } from './types/auth-config.interface';

/**
 * Authentication module for NestJS applications
 * Provides Rails-compatible AES-256-GCM cookie decryption and JWT processing
 */
@Global()
@Module({})
export class AuthModule {
  /**
   * Register the auth module with configuration
   * @param config Authentication configuration
   * @returns Dynamic module
   */
  static register(config: AuthConfig): DynamicModule {
    // Merge with default configuration
    const mergedConfig: AuthConfig = {
      ...DEFAULT_AUTH_CONFIG,
      ...config,
      cookieNames: {
        ...DEFAULT_AUTH_CONFIG.cookieNames,
        ...config.cookieNames,
      },
      jwt: {
        ...DEFAULT_AUTH_CONFIG.jwt,
        ...config.jwt,
      },
    };

    return {
      module: AuthModule,
      imports: [
        HttpModule.register({
          timeout: mergedConfig.requestTimeout || 10000,
          maxRedirects: 5,
        }),
      ],
      controllers: [AuthController],
      providers: [
        {
          provide: AuthConfig,
          useValue: mergedConfig,
        },
        AuthService,
        AuthResolver,
        AuthGuard,
        OptionalAuthGuard,
      ],
      exports: [
        AuthService,
        AuthResolver,
        AuthGuard,
        OptionalAuthGuard,
        AuthConfig,
      ],
    };
  }

  /**
   * Register the auth module asynchronously with configuration factory
   * @param options Async configuration options
   * @returns Dynamic module
   */
  static registerAsync(options: {
    useFactory: (...args: any[]) => Promise<AuthConfig> | AuthConfig;
    inject?: any[];
  }): DynamicModule {
    return {
      module: AuthModule,
      imports: [
        HttpModule.register({
          timeout: 10000,
          maxRedirects: 5,
        }),
      ],
      controllers: [AuthController],
      providers: [
        {
          provide: AuthConfig,
          useFactory: async (...args: any[]) => {
            const config = await options.useFactory(...args);
            return {
              ...DEFAULT_AUTH_CONFIG,
              ...config,
              cookieNames: {
                ...DEFAULT_AUTH_CONFIG.cookieNames,
                ...config.cookieNames,
              },
              jwt: {
                ...DEFAULT_AUTH_CONFIG.jwt,
                ...config.jwt,
              },
            };
          },
          inject: options.inject || [],
        },
        AuthService,
        AuthResolver,
        AuthGuard,
        OptionalAuthGuard,
      ],
      exports: [
        AuthService,
        AuthResolver,
        AuthGuard,
        OptionalAuthGuard,
        AuthConfig,
      ],
    };
  }

  /**
   * Register the auth module with environment variables
   * Automatically reads configuration from environment variables
   * @returns Dynamic module
   */
  static forRoot(): DynamicModule {
    return AuthModule.registerAsync({
      useFactory: () => {
        const config: AuthConfig = {
          encryptionKey: process.env.ACCESS_TOKEN_ENCRYPTION_KEY || '',
          jwksUri: process.env.JWKS_URI,
          externalAuthServiceUrl: process.env.EXTERNAL_AUTH_SERVICE_URL || '',
          cookieNames: {
            accessToken: process.env.ACCESS_TOKEN_COOKIE_NAME || 'access_token',
            refreshToken: process.env.REFRESH_TOKEN_COOKIE_NAME || 'refresh_token',
          },
          requestTimeout: parseInt(process.env.AUTH_REQUEST_TIMEOUT || '10000'),
          enableDebugLogging: process.env.AUTH_DEBUG_LOGGING === 'true',
          jwt: {
            issuer: process.env.JWT_ISSUER,
            audience: process.env.JWT_AUDIENCE,
            skipVerification: process.env.JWT_SKIP_VERIFICATION === 'true',
          },
        };

        // Validate required configuration
        if (!config.encryptionKey) {
          throw new Error('ACCESS_TOKEN_ENCRYPTION_KEY environment variable is required');
        }

        if (!config.externalAuthServiceUrl) {
          throw new Error('EXTERNAL_AUTH_SERVICE_URL environment variable is required');
        }

        return config;
      },
    });
  }
}
