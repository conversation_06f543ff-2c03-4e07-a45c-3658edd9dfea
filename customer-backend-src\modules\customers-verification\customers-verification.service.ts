import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CustomerWithRelations } from '../customers-query/customers-query.service';
import { AuditAction } from '@prisma/client';

// Re-export for convenience
export { CustomerWithRelations } from '../customers-query/customers-query.service';

@Injectable()
export class CustomersVerificationService {
  private readonly logger = new Logger(CustomersVerificationService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Verify customer email
   */
  async verifyEmail(customerId: string): Promise<CustomerWithRelations> {
    this.logger.log(`Verifying email for customer ${customerId}`);

    const customer = await this.prisma.customer.update({
      where: { id: customerId },
      data: {
        isEmailVerified: true,
        updatedAt: new Date(),
      },
      include: {
        addresses: true,
        contacts: true,
        preferences: true,
        auditLogs: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
      },
    });

    // Log the verification action
    await this.prisma.auditLog.create({
      data: {
        customerId,
        action: AuditAction.VERIFICATION,
        entity: 'customer',
        entityId: customerId,
        description: `Email verified`,
        metadata: { verifiedAt: new Date() },
      },
    });

    return customer;
  }

  /**
   * Verify customer phone
   */
  async verifyPhone(customerId: string): Promise<CustomerWithRelations> {
    this.logger.log(`Verifying phone for customer ${customerId}`);

    const customer = await this.prisma.customer.update({
      where: { id: customerId },
      data: {
        isPhoneVerified: true,
        updatedAt: new Date(),
      },
      include: {
        addresses: true,
        contacts: true,
        preferences: true,
        auditLogs: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
      },
    });

    // Log the verification action
    await this.prisma.auditLog.create({
      data: {
        customerId,
        action: AuditAction.VERIFICATION,
        entity: 'customer',
        entityId: customerId,
        description: `Phone verified`,
        metadata: { verifiedAt: new Date() },
      },
    });

    return customer;
  }

  /**
   * Verify customer KYC
   */
  async verifyKyc(customerId: string): Promise<CustomerWithRelations> {
    this.logger.log(`Verifying KYC for customer ${customerId}`);

    const customer = await this.prisma.customer.update({
      where: { id: customerId },
      data: {
        isKycVerified: true,
        updatedAt: new Date(),
      },
      include: {
        addresses: true,
        contacts: true,
        preferences: true,
        auditLogs: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
      },
    });

    // Log the verification action
    await this.prisma.auditLog.create({
      data: {
        customerId,
        action: AuditAction.VERIFICATION,
        entity: 'customer',
        entityId: customerId,
        description: `KYC verified`,
        metadata: { verifiedAt: new Date() },
      },
    });

    return customer;
  }
}
