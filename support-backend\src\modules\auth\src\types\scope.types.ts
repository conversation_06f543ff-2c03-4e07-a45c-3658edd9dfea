/**
 * Scope-based authorization types and definitions
 */

export enum SupportScope {
  // Global admin - full system access
  GLOBAL_SUPPORT_ADMIN = 'global:support:admin',
  
  // Partner-level scopes
  PARTNER_SUPPORT_ADMIN = 'partner:support:admin',
  PARTNER_SUPPORT_USER = 'partner:support:user',
  
  // Account-level scopes
  ACCOUNT_SUPPORT_ADMIN = 'account:support:admin',
  ACCOUNT_SUPPORT_USER = 'account:support:user',
}

export enum TenantType {
  GLOBAL = 'global',
  PARTNER = 'partner',
  ACCOUNT = 'account',
}

export interface ScopeContext {
  scope: SupportScope;
  tenantType: TenantType;
  tenantId?: string; // partner_id or account_id
  userId: string; // sub from JWT
  partnerInfo?: PartnerInfo; // Full partner information
}

export interface UserScopes {
  scopes: string[];
  tenantType: TenantType;
  tenantId?: string;
  userId: string;
}

export interface PartnerInfo {
  id: string;
  public_uid: string;
  name: string;
  // Add other partner fields as needed
}

export interface ScopePermissions {
  canViewAllTickets: boolean;
  canViewOwnTickets: boolean;
  canCreateTickets: boolean;
  canUpdateTickets: boolean;
  canDeleteTickets: boolean;
  canViewSoftDeleted: boolean;
  canManageCategories: boolean;
  canViewCategories: boolean;
  canCreateComments: boolean;
  canUpdateComments: boolean;
  canDeleteComments: boolean;
}

/**
 * Permission matrix based on scope hierarchy
 */
export const SCOPE_PERMISSIONS: Record<SupportScope, ScopePermissions> = {
  [SupportScope.GLOBAL_SUPPORT_ADMIN]: {
    canViewAllTickets: true,
    canViewOwnTickets: true,
    canCreateTickets: true,
    canUpdateTickets: true,
    canDeleteTickets: true,
    canViewSoftDeleted: true,
    canManageCategories: true,
    canViewCategories: true,
    canCreateComments: true,
    canUpdateComments: true,
    canDeleteComments: true,
  },
  [SupportScope.PARTNER_SUPPORT_ADMIN]: {
    canViewAllTickets: true, // Within partner scope
    canViewOwnTickets: true,
    canCreateTickets: true,
    canUpdateTickets: true,
    canDeleteTickets: true,
    canViewSoftDeleted: false,
    canManageCategories: false,
    canViewCategories: true,
    canCreateComments: true,
    canUpdateComments: true,
    canDeleteComments: true,
  },
  [SupportScope.PARTNER_SUPPORT_USER]: {
    canViewAllTickets: false,
    canViewOwnTickets: true,
    canCreateTickets: true,
    canUpdateTickets: true,
    canDeleteTickets: false,
    canViewSoftDeleted: false,
    canManageCategories: false,
    canViewCategories: true,
    canCreateComments: true,
    canUpdateComments: true,
    canDeleteComments: false,
  },
  [SupportScope.ACCOUNT_SUPPORT_ADMIN]: {
    canViewAllTickets: true, // Within account scope
    canViewOwnTickets: true,
    canCreateTickets: true,
    canUpdateTickets: true,
    canDeleteTickets: true,
    canViewSoftDeleted: false,
    canManageCategories: false,
    canViewCategories: true,
    canCreateComments: true,
    canUpdateComments: true,
    canDeleteComments: true,
  },
  [SupportScope.ACCOUNT_SUPPORT_USER]: {
    canViewAllTickets: false,
    canViewOwnTickets: true,
    canCreateTickets: true,
    canUpdateTickets: true,
    canDeleteTickets: false,
    canViewSoftDeleted: false,
    canManageCategories: false,
    canViewCategories: true,
    canCreateComments: true,
    canUpdateComments: true,
    canDeleteComments: false,
  },
};

/**
 * Helper function to get permissions for a scope
 */
export function getScopePermissions(scope: SupportScope): ScopePermissions {
  return SCOPE_PERMISSIONS[scope] || SCOPE_PERMISSIONS[SupportScope.PARTNER_SUPPORT_USER];
}

/**
 * Helper function to determine if a scope allows viewing all tickets within tenant
 */
export function canViewAllTicketsInTenant(scope: SupportScope): boolean {
  return [
    SupportScope.GLOBAL_SUPPORT_ADMIN,
    SupportScope.PARTNER_SUPPORT_ADMIN,
    SupportScope.ACCOUNT_SUPPORT_ADMIN,
  ].includes(scope);
}

/**
 * Helper function to determine if a scope is admin level
 */
export function isAdminScope(scope: SupportScope): boolean {
  return [
    SupportScope.GLOBAL_SUPPORT_ADMIN,
    SupportScope.PARTNER_SUPPORT_ADMIN,
    SupportScope.ACCOUNT_SUPPORT_ADMIN,
  ].includes(scope);
}

/**
 * Helper function to get tenant type from scope
 */
export function getTenantTypeFromScope(scope: SupportScope): TenantType {
  if (scope === SupportScope.GLOBAL_SUPPORT_ADMIN) {
    return TenantType.GLOBAL;
  }
  if (scope.startsWith('partner:')) {
    return TenantType.PARTNER;
  }
  if (scope.startsWith('account:')) {
    return TenantType.ACCOUNT;
  }
  return TenantType.PARTNER; // Default fallback
}
