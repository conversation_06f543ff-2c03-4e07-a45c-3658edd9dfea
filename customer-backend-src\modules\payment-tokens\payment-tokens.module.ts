import { Module } from '@nestjs/common';
import { PaymentTokensController } from './payment-tokens.controller';
import { PaymentTokensService } from './payment-tokens.service';
import { PrismaModule } from '../../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [PaymentTokensController],
  providers: [PaymentTokensService],
  exports: [PaymentTokensService],
})
export class PaymentTokensModule {}
