# Partner API GraphQL Testing Samples

This document contains sample GraphQL queries and mutations for testing the Partner API functionality.

## 🔧 Setup

1. **Access the GraphQL Playground**: Navigate to `http://ng-support-local.dev.dev1.ngnair.com:3040/graphql`
2. **Set Authentication Cookie**: In your browser's developer tools, set the cookie:
   ```
   access_token=R3JZa3UyOHlLQjF4bTYwYWphcWpPOVFNdzJwa0UxdjZpUzd2Q2g0Smd2TDdLSmdjK2VNVkNNalpXVzVIUzhHSmdlV1VJMWFXeEp0dkZMMWpVZ0NjVmNBWDgyT2tNUkVFdElPMkJQekRxWlBQUEU5ZGVrVHdNc2s5N01RTFV0YllHY3JzbFhwWEs4S29VZlNFWlRxamNKODhQSDgwaHdKQW5NTklLZS81ZHRPZkJReTNUOXRHaEJXQmN2WEFHUVUrbXNWWHJhZXdrdGIvMm5XVkNyRkQ0ZHdkclJWTVdzQ1diTHByTzU3K0pDSWsvQ2dYd1VTQ0lId2xjeWwyK0tycm82cTczWFBCMmFISm5uMGZHVWcwVUpHNVgxbjNWN0RmbFVFNmlVRGc1Tzk2M0M5VUtzRTZJSnF3QVhpb1E4Qzl5dEZ3d2Y3L01ZVkhySHBJYkltZkh2TmlLdjQ3VVREL2ErRnRleWxqUUNqYWZKRHN0cllmaklXellSb1JVUVA0bTlkUy83SmhMeXU2bXhBR2llWWxobE9FQm5KZlhwZHREK1FTdVVVaThQdHlJT0ZCdHArenY3a01ma3A4a2FlK2pLYVh5bThRdnJQZm5mODJFdVNTVDl5cHRIcGoyb0Vkd3pOVWhTZGtjWGVQT3B0TTBYZlBhWDV3NFRYVVd3MGdad3lwRXM3amEzODJuQTBET3NpejVRQjJjNXZHRDlNRzY5MVdodS8zSHJ5T3NMNlViZm5jYVdGdnEzQTdRanNseVhyZ2ZxK0VuVkxQWVFEVmVHZ1RndDFGQ3Zna3BLZ280STRCQlFaRG9MZmFnQXQ2U2RsdzlmNW9mYWJsQ3FMWXJreVlZemtWdWYwd3ArOXJMWEZzOXVOVDJkL1Q5b1FsVjkyc2Z3TUd6VnJxT1dpRFQ3b0RDbCtsWXI3amxQamRjQ2c2OFpBbUUvd0dsTENlZitVTnVVcTUwWWR1Q05pWVU3S3pEYWlKaS9yWWlReXp6dXdHa04xdEVqQ2xGY0RLTWVWUmdnSWM0eUVDcXZTWUFQUDdPZ1RvM1dYOXc0OGlrb29IVFdrUWR1cmVsUVcyV3R3UWZ4QmdaZzFUQ0tiOHhSdDdRbDNQTmNZUmV5WWZqZnVRaHRGL0FBWEdRNHIycWtwK09SSmJwakxYanluVDRmcXBkdC9aUzFRY1RmUnRtWDFYUzdjbTloZGoyY2xUSUhhNmQxb041cnYydithRWxuTWQ0ZlpWT0tlSXdkbDJ6TUI4NUdqMnN6UTNZUURUdzUrSVVxNERCV01zZjhyVmpMRHVSZWlIbXJtQkpvM3pIOE4wSlo0TjBtRC9jbTNvUE0zczhVa2lHVkg4NWY4QWJtTW4xY28wajBlS3BFUTJkdEFpUUszSlMwS21mR3ZwN0s0OUc0U1pFUEZDV0JrTitwK3lMOU9OZmtyNGhFUGM2cDVndTlmTUFiNGRQbDUwUFhYRDZvS3ZoWEpONnFodTBxUEhEUT09LS1pdWI1L0JkdFU3WXpSRzZlLS1yWTEvaVZ5LzYwR2JVNTBBOU1aZ3JRPT0%3D
   ```

## 📋 Sample Queries

### 1. Get Partners List

```graphql
query GetPartners {
  getPartners {
    success
    error
    partnersCount
    firstPartner {
      id
      public_uid
      name
      description
    }
    partners {
      id
      public_uid
      name
      description
    }
  }
}
```

**Expected Response:**
```json
{
  "data": {
    "getPartners": {
      "success": true,
      "error": null,
      "partnersCount": 1,
      "firstPartner": {
        "id": "12345",
        "public_uid": "PTR-608-093297",
        "name": "Example Partner",
        "description": "Partner description"
      },
      "partners": [
        {
          "id": "12345",
          "public_uid": "PTR-608-093297",
          "name": "Example Partner",
          "description": "Partner description"
        }
      ]
    }
  }
}
```

### 2. Get User Scopes

```graphql
query GetUserScopes($input: GetUserScopesInput!) {
  getUserScopes(input: $input) {
    success
    error
    scopesCount
    supportScopesCount
    partnerId
    userId
    tenantType
    scopes
    supportScopes
  }
}
```

**Variables:**
```json
{
  "input": {
    "partnerId": "PTR-608-093297",
    "userId": "f7b98e6f-95af-4d54-9c53-312ada49ba6e",
    "tenantType": "partner"
  }
}
```

**Expected Response:**
```json
{
  "data": {
    "getUserScopes": {
      "success": true,
      "error": null,
      "scopesCount": 15,
      "supportScopesCount": 3,
      "partnerId": "PTR-608-093297",
      "userId": "f7b98e6f-95af-4d54-9c53-312ada49ba6e",
      "tenantType": "partner",
      "scopes": [
        "partner:support:admin",
        "partner:support:user",
        "partner:billing:read",
        "partner:users:manage"
      ],
      "supportScopes": [
        "partner:support:admin",
        "partner:support:user"
      ]
    }
  }
}
```

### 3. Test Complete Flow

```graphql
query TestCompleteFlow($input: CompleteFlowInput!) {
  testCompleteFlow(input: $input) {
    success
    error
    partnersStep {
      success
      error
      partnersCount
      firstPartner {
        id
        public_uid
        name
      }
    }
    scopesStep {
      success
      error
      scopesCount
      supportScopesCount
      supportScopes
    }
  }
}
```

**Variables:**
```json
{
  "input": {
    "userId": "f7b98e6f-95af-4d54-9c53-312ada49ba6e",
    "tenantType": "partner"
  }
}
```

**Expected Response:**
```json
{
  "data": {
    "testCompleteFlow": {
      "success": true,
      "error": null,
      "partnersStep": {
        "success": true,
        "error": null,
        "partnersCount": 1,
        "firstPartner": {
          "id": "12345",
          "public_uid": "PTR-608-093297",
          "name": "Example Partner"
        }
      },
      "scopesStep": {
        "success": true,
        "error": null,
        "scopesCount": 15,
        "supportScopesCount": 3,
        "supportScopes": [
          "partner:support:admin",
          "partner:support:user"
        ]
      }
    }
  }
}
```

## 🔍 Testing Different Scenarios

### Test with Account Tenant Type

```graphql
query GetUserScopesAccount($input: GetUserScopesInput!) {
  getUserScopes(input: $input) {
    success
    error
    tenantType
    supportScopes
  }
}
```

**Variables:**
```json
{
  "input": {
    "partnerId": "PTR-608-093297",
    "userId": "f7b98e6f-95af-4d54-9c53-312ada49ba6e",
    "tenantType": "account"
  }
}
```

### Test Error Handling (Invalid Partner ID)

```graphql
query GetUserScopesInvalid($input: GetUserScopesInput!) {
  getUserScopes(input: $input) {
    success
    error
    partnerId
    userId
  }
}
```

**Variables:**
```json
{
  "input": {
    "partnerId": "INVALID-PARTNER-ID",
    "userId": "f7b98e6f-95af-4d54-9c53-312ada49ba6e",
    "tenantType": "partner"
  }
}
```

## 🛠️ Debugging Tips

1. **Check Server Logs**: Monitor the server logs for detailed request/response information:
   ```bash
   docker logs support-backend-api --follow
   ```

2. **Verify Cookie**: Ensure the access token cookie is properly set in your browser

3. **Network Tab**: Use browser developer tools to inspect the actual HTTP requests being made

4. **GraphQL Introspection**: Use the GraphQL playground's schema explorer to see all available fields

## 🔗 Related REST Endpoints

For comparison, the equivalent REST endpoints are:
- `GET /partner-test/partners`
- `GET /partner-test/scopes?partner_id=PTR-608-093297&user_id=USER_ID`
- `GET /partner-test/test-flow?user_id=USER_ID`

## 📝 Notes

- All queries require authentication via the `access_token` cookie
- The GraphQL endpoint automatically applies scope-based authorization
- Support-related scopes are automatically filtered from the full scope list
- Error responses include detailed debugging information
- The complete flow query automatically chains the partners and scopes calls
