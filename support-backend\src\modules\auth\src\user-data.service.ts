import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '../../../config/config.service';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { AxiosResponse } from 'axios';

export interface UserData {
  id: string;
  first_name?: string;
  last_name?: string;
  email?: string;
}

@Injectable()
export class UserDataService {
  private readonly logger = new Logger(UserDataService.name);
  private readonly authApiBaseUrl: string;

  constructor(
    private configService: ConfigService,
    private httpService: HttpService
  ) {
    // Get the auth API base URL from config or use default
    this.authApiBaseUrl = this.configService.get('AUTH_SERVICE_URL') || 'http://localhost:3000';
    this.logger.log(`🔧 [USER DATA SERVICE] Initialized with auth API base URL: ${this.authApiBaseUrl}`);
  }

  /**
   * Fetch user data from the auth API
   * @param userId - The user ID (sub from JWT)
   * @param accessToken - The access token for authentication
   * @returns Promise<UserData> - User data including first_name, last_name, email
   */
  async fetchUserData(userId: string, accessToken?: string): Promise<UserData> {
    try {
      this.logger.log(`👤 [USER DATA SERVICE] Fetching user data for ID: ${userId}`);

      const headers: any = {
        'Content-Type': 'application/json',
        'User-Agent': 'Support-Backend/1.0',
      };

      // Add authentication if access token is provided
      if (accessToken) {
        headers['Cookie'] = `access_token=${accessToken}`;
      }

      // Get all users and filter for the specific user
      const response: AxiosResponse<UserData[]> = await firstValueFrom(
        this.httpService.get(`${this.authApiBaseUrl}/api/v1/users`, {
          headers,
          timeout: 10000, // 10 second timeout
        })
      );

      // Find the specific user by ID
      const userData = response.data.find(user => user.id === userId);
      if (!userData) {
        throw new Error(`User with ID ${userId} not found in user list`);
      }

      if (response.status === 200 && userData) {
        this.logger.log(`✅ [USER DATA SERVICE] Successfully fetched user data for ${userId}`);
        this.logger.log(`📋 [USER DATA SERVICE] User data: ${JSON.stringify({
          id: userData.id,
          first_name: userData.first_name,
          last_name: userData.last_name,
          email: userData.email
        })}`);

        return {
          id: userData.id,
          first_name: userData.first_name,
          last_name: userData.last_name,
          email: userData.email,
        };
      } else {
        throw new HttpException(
          `Invalid response from auth API: ${response.status}`,
          HttpStatus.BAD_GATEWAY
        );
      }
    } catch (error) {
      this.logger.error(`❌ [USER DATA SERVICE] Failed to fetch user data for ${userId}:`, error.message);
      
      if (error.response) {
        this.logger.error(`📋 [USER DATA SERVICE] Response status: ${error.response.status}`);
        this.logger.error(`📋 [USER DATA SERVICE] Response data: ${JSON.stringify(error.response.data)}`);
        
        // Handle specific HTTP errors
        if (error.response.status === 404) {
          throw new HttpException(
            `User not found: ${userId}`,
            HttpStatus.NOT_FOUND
          );
        } else if (error.response.status === 401 || error.response.status === 403) {
          throw new HttpException(
            'Unauthorized to fetch user data',
            HttpStatus.UNAUTHORIZED
          );
        }
      }

      // For network errors or other issues, return minimal user data
      this.logger.warn(`⚠️ [USER DATA SERVICE] Returning minimal user data for ${userId} due to fetch failure`);
      return {
        id: userId,
        first_name: undefined,
        last_name: undefined,
        email: undefined,
      };
    }
  }

  /**
   * Extract access token from request cookies
   * @param cookies - Request cookies object
   * @returns string | undefined - Access token if found
   */
  extractAccessToken(cookies: any): string | undefined {
    if (cookies && cookies.access_token) {
      return cookies.access_token;
    }
    return undefined;
  }

  /**
   * Cache user data to avoid repeated API calls
   * This is a simple in-memory cache - in production you might want to use Redis
   */
  private userDataCache = new Map<string, { data: UserData; timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Get user data with caching
   * @param userId - The user ID
   * @param accessToken - The access token
   * @returns Promise<UserData> - Cached or fresh user data
   */
  async getCachedUserData(userId: string, accessToken?: string): Promise<UserData> {
    const cacheKey = userId;
    const cached = this.userDataCache.get(cacheKey);
    
    // Check if we have valid cached data
    if (cached && (Date.now() - cached.timestamp) < this.CACHE_TTL) {
      this.logger.log(`🎯 [USER DATA SERVICE] Using cached data for user ${userId}`);
      return cached.data;
    }

    // Fetch fresh data
    const userData = await this.fetchUserData(userId, accessToken);
    
    // Cache the result
    this.userDataCache.set(cacheKey, {
      data: userData,
      timestamp: Date.now()
    });

    return userData;
  }

  /**
   * Clear cache for a specific user
   * @param userId - The user ID to clear from cache
   */
  clearUserCache(userId: string): void {
    this.userDataCache.delete(userId);
    this.logger.log(`🗑️ [USER DATA SERVICE] Cleared cache for user ${userId}`);
  }

  /**
   * Clear all cached user data
   */
  clearAllCache(): void {
    this.userDataCache.clear();
    this.logger.log(`🗑️ [USER DATA SERVICE] Cleared all user data cache`);
  }
}
