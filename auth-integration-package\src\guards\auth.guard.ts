import { Injectable, CanActivate, ExecutionContext, UnauthorizedException, Logger } from '@nestjs/common';
import { Request } from 'express';
import { AuthService } from '../auth.service';

/**
 * Authentication guard for protecting routes
 * This guard validates the presence and validity of authentication cookies
 */
@Injectable()
export class AuthGuard implements CanActivate {
  private readonly logger = new Logger(AuthGuard.name);

  constructor(private readonly authService: AuthService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const request = context.switchToHttp().getRequest<Request>();
      
      this.logger.log('🛡️ [AUTH GUARD] Validating authentication');
      
      // Extract cookies from request
      const cookies = request.cookies || {};
      this.logger.log(`🍪 [AUTH GUARD] Available cookies: ${Object.keys(cookies)}`);
      
      // Check if access token is present
      const accessToken = cookies.access_token;
      if (!accessToken) {
        this.logger.error('❌ [AUTH GUARD] No access token found in cookies');
        throw new UnauthorizedException('Missing access token');
      }

      this.logger.log('🔑 [AUTH GUARD] Access token found, validating...');

      // Validate the token by attempting to authenticate the user
      const jwtPayload = await this.authService.authenticateUserFromCookies(cookies);

      // Attach JWT payload to request for use in controllers
      (request as any).user = jwtPayload;

      this.logger.log(`✅ [AUTH GUARD] Authentication successful for user: ${jwtPayload.sub}`);
      return true;
      
    } catch (error) {
      this.logger.error(`❌ [AUTH GUARD] Authentication failed: ${error.message}`);
      throw new UnauthorizedException('Authentication failed');
    }
  }
}

/**
 * Optional auth guard that doesn't throw errors
 * Useful for endpoints that work with or without authentication
 */
@Injectable()
export class OptionalAuthGuard implements CanActivate {
  private readonly logger = new Logger(OptionalAuthGuard.name);

  constructor(private readonly authService: AuthService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const request = context.switchToHttp().getRequest<Request>();
      
      this.logger.log('🛡️ [OPTIONAL AUTH GUARD] Checking for authentication');
      
      // Extract cookies from request
      const cookies = request.cookies || {};
      
      // Check if access token is present
      const accessToken = cookies.access_token;
      if (!accessToken) {
        this.logger.log('ℹ️ [OPTIONAL AUTH GUARD] No access token found, proceeding without authentication');
        return true;
      }

      // Try to validate the token
      const jwtPayload = await this.authService.authenticateUserFromCookies(cookies);

      // Attach JWT payload to request for use in controllers
      (request as any).user = jwtPayload;

      this.logger.log(`✅ [OPTIONAL AUTH GUARD] Authentication successful for user: ${jwtPayload.sub}`);
      return true;
      
    } catch (error) {
      this.logger.warn(`⚠️ [OPTIONAL AUTH GUARD] Authentication failed, proceeding without auth: ${error.message}`);
      // Don't throw error, just proceed without authentication
      return true;
    }
  }
}
